/**
 * SystemInfo.js
 *
 * System information gathering utility module providing comprehensive
 * information about the operating system, environment, and installed tools.
 *
 * This module handles:
 * - Operating system detection
 * - System resource information
 * - Tool version detection
 * - Environment variable access
 */

const os = require('os');
const { exec } = require('child_process');
const { promisify } = require('util');
const path = require('path');

const fs = require('fs-extra');

const execAsync = promisify(exec);

/**
 * SystemInfo Class
 *
 * Provides methods for gathering comprehensive system information
 * and detecting installed development tools.
 */
class SystemInfo {
  constructor() {
    this.platform = os.platform();
    this.homeDir = os.homedir();
  }

  /**
   * Get comprehensive system information
   *
   * @returns {object} System information object
   */
  async getSystemInfo() {
    const info = {
      // Basic system information
      platform: this.platform,
      platformName: this.getPlatformName(),
      architecture: os.arch(),
      nodeVersion: process.version,
      npmVersion: await this.getNpmVersion(),
      homeDirectory: this.homeDir,
      currentDirectory: process.cwd(),
      tempDirectory: os.tmpdir(),

      // System resources
      cpuCount: os.cpus().length,
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      uptime: os.uptime(),

      // User information
      username: os.userInfo().username,
      hostname: os.hostname(),

      // Environment
      shell: process.env.SHELL || 'unknown',
      isWSL: await this.isWSL(),
      isDocker: await this.isDocker(),

      // Development tools
      gitVersion: await this.getToolVersion('git --version'),
      claudeVersion: await this.getToolVersion('claude --version'),
      pythonVersion: await this.getToolVersion('python --version', 'python3 --version'),
      dockerVersion: await this.getToolVersion('docker --version'),
      nodePackageManager: await this.detectPackageManager(),
    };

    return info;
  }

  /**
   * Get human-readable platform name
   *
   * @returns {string} Platform name
   */
  getPlatformName() {
    const platformNames = {
      win32: 'Windows',
      darwin: 'macOS',
      linux: 'Linux',
      freebsd: 'FreeBSD',
      sunos: 'SunOS',
      aix: 'AIX',
    };

    return platformNames[this.platform] || this.platform;
  }

  /**
   * Get tool version with fallback commands
   *
   * @param {...string} commands - Commands to try in order
   * @returns {string} Version string or 'Not installed'
   */
  async getToolVersion(...commands) {
    for (const command of commands) {
      try {
        const { stdout } = await execAsync(command);
        return stdout.trim();
      } catch (error) {
        // Try next command
      }
    }
    return 'Not installed';
  }

  /**
   * Get NPM version
   *
   * @returns {string} NPM version or 'Not installed'
   */
  async getNpmVersion() {
    try {
      const { stdout } = await execAsync('npm --version');
      return stdout.trim();
    } catch (error) {
      return 'Not installed';
    }
  }

  /**
   * Detect installed Node.js package manager
   *
   * @returns {string} Package manager name
   */
  async detectPackageManager() {
    const managers = [
      { name: 'pnpm', command: 'pnpm --version' },
      { name: 'yarn', command: 'yarn --version' },
      { name: 'bun', command: 'bun --version' },
      { name: 'npm', command: 'npm --version' },
    ];

    for (const { name, command } of managers) {
      try {
        await execAsync(command);
        return name;
      } catch (error) {
        // Try next manager
      }
    }

    return 'none';
  }

  /**
   * Check if running in Windows Subsystem for Linux
   *
   * @returns {boolean} True if running in WSL
   */
  async isWSL() {
    if (this.platform !== 'linux') {
      return false;
    }

    try {
      const procVersion = await fs.readFile('/proc/version', 'utf8');
      return procVersion.toLowerCase().includes('microsoft');
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if running inside Docker container
   *
   * @returns {boolean} True if running in Docker
   */
  async isDocker() {
    try {
      await fs.access('/.dockerenv');
      return true;
    } catch (error) {
      try {
        const cgroup = await fs.readFile('/proc/self/cgroup', 'utf8');
        return cgroup.includes('docker');
      } catch (error) {
        return false;
      }
    }
  }

  /**
   * Get system environment variables
   *
   * @param {Array<string>} keys - Specific keys to retrieve (optional)
   * @returns {object} Environment variables
   */
  getEnvironmentVariables(keys = null) {
    if (keys) {
      const result = {};
      for (const key of keys) {
        result[key] = process.env[key] || null;
      }
      return result;
    }
    return process.env;
  }

  /**
   * Get disk usage for a path
   *
   * @param {string} targetPath - Path to check
   * @returns {object} Disk usage information
   */
  async getDiskUsage(targetPath) {
    const platform = this.platform;
    let command;

    if (platform === 'win32') {
      // Windows command to get disk usage
      const drive = path.parse(targetPath).root;
      command = `wmic logicaldisk where "DeviceID='${drive.replace('\\', '')}'" get Size,FreeSpace /value`;
    } else {
      // Unix-like command
      command = `df -k "${targetPath}" | tail -1`;
    }

    try {
      const { stdout } = await execAsync(command);

      if (platform === 'win32') {
        // Parse Windows output
        const freeSpace = stdout.match(/FreeSpace=(\d+)/);
        const size = stdout.match(/Size=(\d+)/);
        return {
          total: size ? parseInt(size[1]) : 0,
          free: freeSpace ? parseInt(freeSpace[1]) : 0,
          used: size && freeSpace ? parseInt(size[1]) - parseInt(freeSpace[1]) : 0,
        };
      }
      // Parse Unix output
      const parts = stdout.trim().split(/\s+/);
      return {
        total: parseInt(parts[1]) * 1024,
        used: parseInt(parts[2]) * 1024,
        free: parseInt(parts[3]) * 1024,
      };
    } catch (error) {
      return { error: `Failed to get disk usage: ${error.message}` };
    }
  }

  /**
   * Format bytes to human readable format
   *
   * @param {number} bytes - Number of bytes
   * @param {number} decimals - Decimal places
   * @returns {string} Formatted string
   */
  formatBytes(bytes, decimals = 2) {
    if (bytes === 0) {
      return '0 Bytes';
    }

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
  }

  /**
   * Get network interfaces information
   *
   * @returns {object} Network interfaces
   */
  getNetworkInterfaces() {
    const interfaces = os.networkInterfaces();
    const result = {};

    for (const [name, addresses] of Object.entries(interfaces)) {
      result[name] = addresses
        .filter((addr) => !addr.internal)
        .map((addr) => ({
          family: addr.family,
          address: addr.address,
          mac: addr.mac,
        }));
    }

    return result;
  }

  /**
   * Check if a command is available in PATH
   *
   * @param {string} command - Command to check
   * @returns {boolean} True if command is available
   */
  async isCommandAvailable(command) {
    try {
      await execAsync(`${this.platform === 'win32' ? 'where' : 'which'} ${command}`);
      return true;
    } catch (error) {
      return false;
    }
  }
}

module.exports = SystemInfo;
