/**
 * BackupOperations.js
 *
 * Backup and restore operations utility module providing safe file operations
 * with automatic backup functionality.
 *
 * This module handles:
 * - Automatic file backups before overwriting
 * - Timestamped backup file naming
 * - Backup restoration capabilities
 * - Backup cleanup and management
 */

const path = require('path');

const fs = require('fs-extra');

/**
 * BackupOperations Class
 *
 * Provides backup and restore functionality for safe file operations.
 */
class BackupOperations {
  /**
   * Copy file with automatic backup of existing target
   *
   * @param {string} source - Source file path
   * @param {string} target - Target file path
   * @returns {object} Backup operation result
   */
  async copyFileWithBackup(source, target) {
    const result = {
      copied: false,
      backupCreated: false,
      backupPath: null,
      targetExisted: false,
    };

    try {
      // Check if target file exists
      const targetExists = await fs.pathExists(target);
      result.targetExisted = targetExists;

      // Create backup if target exists
      if (targetExists) {
        const backupPath = this.generateBackupPath(target);
        await fs.copy(target, backupPath);
        result.backupCreated = true;
        result.backupPath = backupPath;
        console.log(`Backed up existing file to: ${backupPath}`);
      }

      // Ensure target directory exists
      await fs.ensureDir(path.dirname(target));

      // Copy source to target
      await fs.copy(source, target);
      result.copied = true;

      return result;
    } catch (error) {
      throw new Error(
        `Failed to copy file with backup from ${source} to ${target}: ${error.message}`,
      );
    }
  }

  /**
   * Generate backup file path with timestamp
   *
   * @param {string} filePath - Original file path
   * @param {Date} timestamp - Optional timestamp (defaults to now)
   * @returns {string} Backup file path
   */
  generateBackupPath(filePath, timestamp = new Date()) {
    const dir = path.dirname(filePath);
    const ext = path.extname(filePath);
    const name = path.basename(filePath, ext);
    const timeStr = timestamp.toISOString().replace(/[:.]/g, '-');

    return path.join(dir, `${name}.backup.${timeStr}${ext}`);
  }

  /**
   * Create backup of a file
   *
   * @param {string} filePath - File to backup
   * @param {string} backupDir - Optional backup directory
   * @returns {string} Path to backup file
   */
  async createBackup(filePath, backupDir = null) {
    if (!(await fs.pathExists(filePath))) {
      throw new Error(`File does not exist: ${filePath}`);
    }

    let backupPath;
    if (backupDir) {
      await fs.ensureDir(backupDir);
      const filename = path.basename(filePath);
      const timestamp = Date.now();
      backupPath = path.join(backupDir, `${filename}.backup.${timestamp}`);
    } else {
      backupPath = this.generateBackupPath(filePath);
    }

    await fs.copy(filePath, backupPath);
    return backupPath;
  }

  /**
   * Restore file from backup
   *
   * @param {string} backupPath - Path to backup file
   * @param {string} targetPath - Path to restore to
   * @returns {boolean} True if restoration succeeded
   */
  async restoreFromBackup(backupPath, targetPath) {
    if (!(await fs.pathExists(backupPath))) {
      throw new Error(`Backup file does not exist: ${backupPath}`);
    }

    try {
      // Ensure target directory exists
      await fs.ensureDir(path.dirname(targetPath));

      // Copy backup to target location
      await fs.copy(backupPath, targetPath);
      return true;
    } catch (error) {
      throw new Error(
        `Failed to restore from backup ${backupPath} to ${targetPath}: ${error.message}`,
      );
    }
  }

  /**
   * Find backup files for a given file
   *
   * @param {string} filePath - Original file path
   * @param {string} backupDir - Optional backup directory to search
   * @returns {Array<object>} Array of backup file information
   */
  async findBackups(filePath, backupDir = null) {
    const searchDir = backupDir || path.dirname(filePath);
    const originalName = path.basename(filePath);

    try {
      const files = await fs.readdir(searchDir);
      const backups = [];

      for (const file of files) {
        if (file.startsWith(`${path.parse(originalName).name}.backup.`)) {
          const backupPath = path.join(searchDir, file);
          const stats = await fs.stat(backupPath);

          backups.push({
            path: backupPath,
            filename: file,
            created: stats.mtime,
            size: stats.size,
          });
        }
      }

      // Sort by creation time (newest first)
      return backups.sort((a, b) => b.created - a.created);
    } catch (error) {
      throw new Error(`Failed to find backups for ${filePath}: ${error.message}`);
    }
  }

  /**
   * Clean up old backup files
   *
   * @param {string} filePath - Original file path
   * @param {number} keepCount - Number of backups to keep (default: 5)
   * @param {string} backupDir - Optional backup directory
   * @returns {number} Number of backups deleted
   */
  async cleanupOldBackups(filePath, keepCount = 5, backupDir = null) {
    const backups = await this.findBackups(filePath, backupDir);

    if (backups.length <= keepCount) {
      return 0; // No cleanup needed
    }

    const toDelete = backups.slice(keepCount);
    let deletedCount = 0;

    for (const backup of toDelete) {
      try {
        await fs.unlink(backup.path);
        deletedCount++;
      } catch (error) {
        console.warn(`Failed to delete backup ${backup.path}: ${error.message}`);
      }
    }

    return deletedCount;
  }

  /**
   * Get backup information
   *
   * @param {string} backupPath - Path to backup file
   * @returns {object} Backup file information
   */
  async getBackupInfo(backupPath) {
    if (!(await fs.pathExists(backupPath))) {
      throw new Error(`Backup file does not exist: ${backupPath}`);
    }

    const stats = await fs.stat(backupPath);
    const filename = path.basename(backupPath);

    // Extract timestamp from filename
    const timestampMatch = filename.match(/\.backup\.([^.]+)/);
    const timestamp = timestampMatch ? timestampMatch[1] : 'unknown';

    return {
      path: backupPath,
      filename,
      size: stats.size,
      created: stats.mtime,
      timestamp,
      isValid: await this.validateBackup(backupPath),
    };
  }

  /**
   * Validate backup file integrity
   *
   * @param {string} backupPath - Path to backup file
   * @returns {boolean} True if backup is valid
   */
  async validateBackup(backupPath) {
    try {
      const stats = await fs.stat(backupPath);
      // Basic validation: file exists and has size > 0
      return stats.isFile() && stats.size > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Create backup directory structure
   *
   * @param {string} baseDir - Base directory for backups
   * @param {string} projectName - Project name for organization
   * @returns {string} Created backup directory path
   */
  async createBackupDirectory(baseDir, projectName) {
    const backupDir = path.join(baseDir, 'backups', projectName);
    await fs.ensureDir(backupDir);
    return backupDir;
  }
}

module.exports = BackupOperations;
