/**
 * DirectoryOperations.js
 *
 * Directory operations utility module providing essential directory management
 * functionality with error handling and cross-platform compatibility.
 *
 * This module handles:
 * - Directory existence and emptiness checks
 * - Directory creation and deletion
 * - Directory reading and traversal
 * - Directory permission validation
 */

const path = require('path');

const fs = require('fs-extra');

/**
 * DirectoryOperations Class
 *
 * Provides comprehensive directory operations with consistent error handling
 * and cross-platform compatibility.
 */
class DirectoryOperations {
  /**
   * Check if a directory exists
   *
   * @param {string} dirPath - Absolute path to check
   * @returns {boolean} True if directory exists, false otherwise
   */
  async directoryExists(dirPath) {
    try {
      const stats = await fs.stat(dirPath);
      return stats.isDirectory();
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if a directory is empty
   *
   * @param {string} dirPath - Absolute path to the directory to check
   * @returns {boolean} True if directory is empty or doesn't exist, false if it contains files
   */
  async isDirectoryEmpty(dirPath) {
    try {
      const files = await fs.readdir(dirPath);
      return files.length === 0;
    } catch (error) {
      // Directory doesn't exist or can't be accessed - consider it "empty" for installation purposes
      return true;
    }
  }

  /**
   * Create a directory (including parent directories)
   *
   * @param {string} dirPath - Absolute path to the directory to create
   * @returns {boolean} True if directory was created or already exists
   * @throws {Error} If directory cannot be created
   */
  async createDirectory(dirPath) {
    try {
      await fs.ensureDir(dirPath);
      return true;
    } catch (error) {
      throw new Error(`Failed to create directory ${dirPath}: ${error.message}`);
    }
  }

  /**
   * Remove a directory and all its contents
   *
   * @param {string} dirPath - Absolute path to the directory to remove
   * @throws {Error} If directory cannot be removed
   */
  async removeDirectory(dirPath) {
    try {
      await fs.remove(dirPath);
    } catch (error) {
      throw new Error(`Failed to remove directory ${dirPath}: ${error.message}`);
    }
  }

  /**
   * Read directory contents
   *
   * @param {string} dirPath - Absolute path to the directory to read
   * @param {object} options - Read options (e.g., { withFileTypes: true })
   * @returns {Array} Array of filenames or file objects
   * @throws {Error} If directory cannot be read
   */
  async readDirectory(dirPath, options = {}) {
    try {
      const contents = await fs.readdir(dirPath, options);
      return contents;
    } catch (error) {
      throw new Error(`Failed to read directory ${dirPath}: ${error.message}`);
    }
  }

  /**
   * Copy directory from source to destination
   *
   * @param {string} source - Source directory path
   * @param {string} destination - Destination directory path
   * @param {object} options - Copy options
   * @throws {Error} If directory cannot be copied
   */
  async copyDirectory(source, destination, options = {}) {
    try {
      await fs.copy(source, destination, options);
    } catch (error) {
      throw new Error(
        `Failed to copy directory from ${source} to ${destination}: ${error.message}`,
      );
    }
  }

  /**
   * Ensure directory is writable
   *
   * @param {string} dirPath - Absolute path to the directory to validate
   * @returns {boolean} True if directory is writable
   * @throws {Error} If directory cannot be created or is not writable
   */
  async ensureDirectoryWritable(dirPath) {
    try {
      // Ensure directory exists (creates parent directories as needed)
      await fs.ensureDir(dirPath);
      // Test write permissions using Node.js access check
      await fs.access(dirPath, fs.constants.W_OK);
      return true;
    } catch (error) {
      throw new Error(`Directory ${dirPath} is not writable: ${error.message}`);
    }
  }

  /**
   * Get directory size (recursive)
   *
   * @param {string} dirPath - Absolute path to the directory
   * @returns {number} Total size in bytes
   */
  async getDirectorySize(dirPath) {
    let totalSize = 0;

    const calculateSize = async (currentPath) => {
      const stats = await fs.stat(currentPath);

      if (stats.isFile()) {
        totalSize += stats.size;
      } else if (stats.isDirectory()) {
        const files = await fs.readdir(currentPath);
        await Promise.all(files.map((file) => calculateSize(path.join(currentPath, file))));
      }
    };

    try {
      await calculateSize(dirPath);
      return totalSize;
    } catch (error) {
      throw new Error(`Failed to calculate directory size for ${dirPath}: ${error.message}`);
    }
  }

  /**
   * Move/rename a directory
   *
   * @param {string} source - Current directory path
   * @param {string} destination - New directory path
   * @throws {Error} If directory cannot be moved
   */
  async moveDirectory(source, destination) {
    try {
      await fs.move(source, destination);
    } catch (error) {
      throw new Error(
        `Failed to move directory from ${source} to ${destination}: ${error.message}`,
      );
    }
  }

  /**
   * Find files in directory matching a pattern
   *
   * @param {string} dirPath - Directory to search in
   * @param {RegExp|Function} pattern - Pattern to match files against
   * @param {boolean} recursive - Search recursively in subdirectories
   * @returns {Array<string>} Array of matching file paths
   */
  async findFiles(dirPath, pattern, recursive = false) {
    const matches = [];

    const searchDir = async (currentPath) => {
      const entries = await fs.readdir(currentPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(currentPath, entry.name);

        if (entry.isFile()) {
          const isMatch =
            pattern instanceof RegExp ? pattern.test(entry.name) : pattern(entry.name, fullPath);

          if (isMatch) {
            matches.push(fullPath);
          }
        } else if (entry.isDirectory() && recursive) {
          await searchDir(fullPath);
        }
      }
    };

    try {
      await searchDir(dirPath);
      return matches;
    } catch (error) {
      throw new Error(`Failed to search directory ${dirPath}: ${error.message}`);
    }
  }
}

module.exports = DirectoryOperations;
