/**
 * FileOperations.js
 *
 * Basic file operations utility module providing essential file system operations
 * with error handling and cross-platform compatibility.
 *
 * This module handles:
 * - File reading and writing
 * - File copying with backup functionality
 * - File existence checking
 * - Safe file operations with proper error handling
 */

const path = require('path');

const fs = require('fs-extra');

/**
 * FileOperations Class
 *
 * Provides basic file operations with consistent error handling
 * and cross-platform compatibility.
 */
class FileOperations {
  /**
   * Read file content as UTF-8 text
   *
   * @param {string} filePath - Absolute path to the file to read
   * @returns {string} File content as string
   * @throws {Error} If file cannot be read or doesn't exist
   */
  async readFile(filePath) {
    try {
      // Read file content as UTF-8 text
      const content = await fs.readFile(filePath, 'utf8');
      return content;
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error.message}`);
    }
  }

  /**
   * Write content to a file
   *
   * @param {string} filePath - Absolute path where the file should be written
   * @param {string} content - Content to write to the file
   * @param {string} encoding - File encoding (default: 'utf8')
   * @throws {Error} If file cannot be written
   */
  async writeFile(filePath, content, encoding = 'utf8') {
    try {
      // Ensure parent directory exists
      await fs.ensureDir(path.dirname(filePath));
      // Write content to file with specified encoding
      await fs.writeFile(filePath, content, encoding);
    } catch (error) {
      throw new Error(`Failed to write file ${filePath}: ${error.message}`);
    }
  }

  /**
   * Copy file from source to destination
   *
   * @param {string} source - Absolute path to the source file
   * @param {string} destination - Absolute path to the destination
   * @param {object} options - Copy options (e.g., { overwrite: true })
   * @throws {Error} If file cannot be copied
   */
  async copyFile(source, destination, options = {}) {
    try {
      // Ensure destination directory exists
      await fs.ensureDir(path.dirname(destination));
      // Copy file with options
      await fs.copy(source, destination, options);
    } catch (error) {
      throw new Error(`Failed to copy file from ${source} to ${destination}: ${error.message}`);
    }
  }

  /**
   * Check if a file exists
   *
   * @param {string} filePath - Absolute path to check
   * @returns {boolean} True if file exists, false otherwise
   */
  async fileExists(filePath) {
    try {
      await fs.access(filePath, fs.constants.F_OK);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get file stats
   *
   * @param {string} filePath - Absolute path to the file
   * @returns {object|null} File stats object or null if file doesn't exist
   */
  async getFileStats(filePath) {
    try {
      const stats = await fs.stat(filePath);
      return stats;
    } catch (error) {
      return null;
    }
  }

  /**
   * Delete a file
   *
   * @param {string} filePath - Absolute path to the file to delete
   * @throws {Error} If file cannot be deleted
   */
  async deleteFile(filePath) {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      throw new Error(`Failed to delete file ${filePath}: ${error.message}`);
    }
  }

  /**
   * Move/rename a file
   *
   * @param {string} source - Current file path
   * @param {string} destination - New file path
   * @throws {Error} If file cannot be moved
   */
  async moveFile(source, destination) {
    try {
      // Ensure destination directory exists
      await fs.ensureDir(path.dirname(destination));
      // Move file
      await fs.move(source, destination);
    } catch (error) {
      throw new Error(`Failed to move file from ${source} to ${destination}: ${error.message}`);
    }
  }
}

module.exports = FileOperations;
