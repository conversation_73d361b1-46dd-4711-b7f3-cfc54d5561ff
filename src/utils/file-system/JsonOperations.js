/**
 * JsonOperations.js
 *
 * JSON file operations utility module providing safe JSON reading, writing,
 * and merging capabilities with proper error handling.
 *
 * This module handles:
 * - Safe JSON file reading with error handling
 * - Formatted JSON file writing
 * - Intelligent JSON merging (especially for package.json)
 * - JSON validation and parsing
 */

const path = require('path');

const fs = require('fs-extra');

/**
 * JsonOperations Class
 *
 * Provides JSON-specific file operations with validation,
 * formatting, and intelligent merging capabilities.
 */
class JsonOperations {
  /**
   * Read and parse a JSON file
   *
   * @param {string} filePath - Absolute path to the JSON file
   * @returns {object} Parsed JSON object
   * @throws {Error} If file doesn't exist or contains invalid JSON
   */
  async readJsonFile(filePath) {
    try {
      // Read file content as UTF-8 text
      const content = await fs.readFile(filePath, 'utf8');
      // Parse JSON content and return the resulting object
      return JSON.parse(content);
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error(`JSON file not found: ${filePath}`);
      } else if (error instanceof SyntaxError) {
        throw new Error(`Invalid JSON in file ${filePath}: ${error.message}`);
      } else {
        throw new Error(`Failed to read JSON file ${filePath}: ${error.message}`);
      }
    }
  }

  /**
   * Write object to JSON file with formatting
   *
   * @param {string} filePath - Absolute path where JSON should be written
   * @param {object} data - JavaScript object to serialize
   * @param {number} spaces - Number of spaces for indentation (default: 2)
   * @throws {Error} If file cannot be written
   */
  async writeJsonFile(filePath, data, spaces = 2) {
    try {
      // Ensure parent directory exists
      await fs.ensureDir(path.dirname(filePath));
      // Serialize object to formatted JSON string with specified indentation
      const content = JSON.stringify(data, null, spaces);
      // Write formatted JSON content to file with UTF-8 encoding
      await fs.writeFile(filePath, content, 'utf8');
    } catch (error) {
      throw new Error(`Failed to write JSON file ${filePath}: ${error.message}`);
    }
  }

  /**
   * Safely update a JSON file by reading, modifying, and writing back
   *
   * @param {string} filePath - Path to JSON file to update
   * @param {Function} updateFn - Function that receives data and returns modified data
   * @param {object} defaultData - Default data if file doesn't exist
   * @returns {object} Updated data
   */
  async updateJsonFile(filePath, updateFn, defaultData = {}) {
    try {
      // Read existing data or use default
      let data;
      try {
        data = await this.readJsonFile(filePath);
      } catch (error) {
        if (error.message.includes('not found')) {
          data = defaultData;
        } else {
          throw error;
        }
      }

      // Apply update function
      const updatedData = await updateFn(data);

      // Write back to file
      await this.writeJsonFile(filePath, updatedData);

      return updatedData;
    } catch (error) {
      throw new Error(`Failed to update JSON file ${filePath}: ${error.message}`);
    }
  }

  /**
   * Merge package.json intelligently
   *
   * @param {string} targetPath - Directory containing package.json
   * @param {object} additions - Sections to merge into package.json
   * @returns {object} Complete merged package.json
   */
  async mergePackageJson(targetPath, additions) {
    const packageJsonPath = path.join(targetPath, 'package.json');

    return await this.updateJsonFile(
      packageJsonPath,
      (packageJson) => {
        // Merge scripts (new scripts overwrite existing)
        if (additions.scripts) {
          packageJson.scripts = {
            ...packageJson.scripts,
            ...additions.scripts,
          };
        }

        // Merge dependencies (new versions overwrite existing)
        if (additions.dependencies) {
          packageJson.dependencies = {
            ...packageJson.dependencies,
            ...additions.dependencies,
          };
        }

        // Merge devDependencies (new versions overwrite existing)
        if (additions.devDependencies) {
          packageJson.devDependencies = {
            ...packageJson.devDependencies,
            ...additions.devDependencies,
          };
        }

        // Merge keywords with deduplication
        if (additions.keywords) {
          const existingKeywords = packageJson.keywords || [];
          packageJson.keywords = [...new Set([...existingKeywords, ...additions.keywords])];
        }

        // Merge other top-level properties
        const { scripts, dependencies, devDependencies, keywords, ...otherAdditions } = additions;
        Object.assign(packageJson, otherAdditions);

        return packageJson;
      },
      {
        name: path.basename(targetPath),
        version: '1.0.0',
        description: '',
        main: 'index.js',
        scripts: {},
        keywords: [],
        author: '',
        license: 'ISC',
      },
    );
  }

  /**
   * Validate JSON string
   *
   * @param {string} jsonString - JSON string to validate
   * @returns {object} Validation result with parsed data or error
   */
  validateJson(jsonString) {
    try {
      const parsed = JSON.parse(jsonString);
      return { valid: true, data: parsed };
    } catch (error) {
      return { valid: false, error: error.message };
    }
  }

  /**
   * Deep merge two JSON objects
   *
   * @param {object} target - Target object
   * @param {object} source - Source object to merge
   * @returns {object} Merged object (new object, doesn't mutate inputs)
   */
  deepMerge(target, source) {
    const result = { ...target };

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          if (result[key] && typeof result[key] === 'object' && !Array.isArray(result[key])) {
            result[key] = this.deepMerge(result[key], source[key]);
          } else {
            result[key] = source[key];
          }
        } else {
          result[key] = source[key];
        }
      }
    }

    return result;
  }

  /**
   * Read JSON file with fallback to default
   *
   * @param {string} filePath - Path to JSON file
   * @param {object} defaultValue - Default value if file doesn't exist
   * @returns {object} JSON data or default value
   */
  async readJsonFileWithDefault(filePath, defaultValue = {}) {
    try {
      return await this.readJsonFile(filePath);
    } catch (error) {
      if (error.message.includes('not found')) {
        return defaultValue;
      }
      throw error;
    }
  }

  /**
   * Pretty print JSON to console
   *
   * @param {object} data - Data to print
   * @param {string} label - Optional label to print before data
   */
  prettyPrint(data, label = '') {
    if (label) {
      console.log(`\n${label}:`);
    }
    console.log(JSON.stringify(data, null, 2));
  }
}

module.exports = JsonOperations;
