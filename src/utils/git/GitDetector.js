/**
 * GitDetector.js
 *
 * Git repository detection utility module providing Git-specific checks
 * and repository status information.
 *
 * This module handles:
 * - Git repository detection
 * - Git worktree detection
 * - Git status checks
 * - Repository type identification
 */

const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const fs = require('fs-extra');

const execAsync = promisify(exec);

/**
 * GitDetector Class
 *
 * Provides Git repository detection and status checking capabilities
 * with proper error handling.
 */
class GitDetector {
  /**
   * Check if a directory is a Git repository
   *
   * @param {string} dirPath - Absolute path to check
   * @returns {boolean} True if directory is a Git repository
   */
  async isGitRepository(dirPath) {
    try {
      const gitDir = path.join(dirPath, '.git');
      const stats = await fs.stat(gitDir);

      // Check if .git is a directory (regular repo) or file (worktree/submodule)
      if (stats.isDirectory()) {
        return true;
      }
      if (stats.isFile()) {
        // .git file indicates a worktree or submodule
        const content = await fs.readFile(gitDir, 'utf8');
        return content.startsWith('gitdir:');
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if a directory is a Git worktree
   *
   * @param {string} dirPath - Absolute path to check
   * @returns {boolean} True if directory is a Git worktree
   */
  async isGitWorktree(dirPath) {
    try {
      const gitFile = path.join(dirPath, '.git');
      const stats = await fs.stat(gitFile);

      if (stats.isFile()) {
        const content = await fs.readFile(gitFile, 'utf8');
        return content.startsWith('gitdir:') && content.includes('/worktrees/');
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get the main repository path from a worktree
   *
   * @param {string} worktreePath - Path to the worktree
   * @returns {string|null} Path to main repository or null
   */
  async getMainRepositoryPath(worktreePath) {
    try {
      const gitFile = path.join(worktreePath, '.git');
      const content = await fs.readFile(gitFile, 'utf8');

      if (content.startsWith('gitdir:')) {
        const gitDirPath = content.replace('gitdir:', '').trim();
        // Navigate from worktree git dir to main repo
        // Format: /path/to/repo/.git/worktrees/branch-name
        const parts = gitDirPath.split('/worktrees/');
        if (parts.length === 2) {
          return path.dirname(parts[0]); // Remove .git from path
        }
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if Git is installed and available
   *
   * @returns {object} Installation status and version
   */
  async checkGitInstallation() {
    try {
      const { stdout } = await execAsync('git --version');
      const version = stdout.trim();
      return {
        installed: true,
        version: version.replace('git version ', ''),
      };
    } catch (error) {
      return {
        installed: false,
        error: 'Git is not installed or not in PATH',
      };
    }
  }

  /**
   * Get repository status summary
   *
   * @param {string} repoPath - Path to Git repository
   * @returns {object|null} Repository status information
   */
  async getRepositoryStatus(repoPath) {
    if (!(await this.isGitRepository(repoPath))) {
      return null;
    }

    try {
      // Get various Git status information
      const [status, branch, remotes] = await Promise.all([
        execAsync('git status --porcelain', { cwd: repoPath }),
        execAsync('git branch --show-current', { cwd: repoPath }),
        execAsync('git remote -v', { cwd: repoPath }),
      ]);

      const modifiedFiles = status.stdout.split('\n').filter((line) => line.trim()).length;
      const currentBranch = branch.stdout.trim();
      const hasRemotes = remotes.stdout.trim().length > 0;

      return {
        currentBranch,
        hasChanges: modifiedFiles > 0,
        modifiedFiles,
        hasRemotes,
        isWorktree: await this.isGitWorktree(repoPath),
      };
    } catch (error) {
      return {
        error: `Failed to get repository status: ${error.message}`,
      };
    }
  }

  /**
   * Find the root of a Git repository from a subdirectory
   *
   * @param {string} startPath - Starting directory path
   * @returns {string|null} Repository root path or null
   */
  async findRepositoryRoot(startPath) {
    let currentPath = path.resolve(startPath);

    while (currentPath !== path.parse(currentPath).root) {
      if (await this.isGitRepository(currentPath)) {
        return currentPath;
      }
      currentPath = path.dirname(currentPath);
    }

    return null;
  }

  /**
   * Check if a path is ignored by Git
   *
   * @param {string} filePath - Path to check
   * @param {string} repoPath - Repository root path
   * @returns {boolean} True if path is ignored
   */
  async isIgnoredByGit(filePath, repoPath) {
    try {
      const relativePath = path.relative(repoPath, filePath);
      await execAsync(`git check-ignore ${relativePath}`, { cwd: repoPath });
      return true; // Command succeeds if file is ignored
    } catch (error) {
      return false; // Command fails if file is not ignored
    }
  }
}

module.exports = GitDetector;
