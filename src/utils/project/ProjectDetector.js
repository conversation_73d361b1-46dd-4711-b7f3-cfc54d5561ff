/**
 * ProjectDetector.js
 *
 * Project type detection utility module for identifying programming languages,
 * frameworks, and package managers used in a project.
 *
 * This module handles:
 * - Programming language detection
 * - Framework identification
 * - Package manager detection
 * - Project structure analysis
 */

const path = require('path');

const fs = require('fs-extra');

/**
 * ProjectDetector Class
 *
 * Provides comprehensive project type detection by analyzing
 * configuration files and project structure.
 */
class ProjectDetector {
  constructor() {
    // Primary project type indicators based on configuration files
    this.languageIndicators = {
      'package.json': 'node',
      'Cargo.toml': 'rust',
      'pyproject.toml': 'python',
      'go.mod': 'go',
      'composer.json': 'php',
      'pom.xml': 'java',
      Gemfile: 'ruby',
      'requirements.txt': 'python',
      'setup.py': 'python',
      'build.gradle': 'java',
      'build.gradle.kts': 'java',
      'CMakeLists.txt': 'cpp',
      Makefile: 'make',
      '.csproj': 'csharp',
      'pubspec.yaml': 'dart',
      'mix.exs': 'elixir',
      'project.clj': 'clojure',
      'build.sbt': 'scala',
      'Package.swift': 'swift',
    };

    // Package manager indicators
    this.packageManagerIndicators = {
      'yarn.lock': 'yarn',
      'pnpm-lock.yaml': 'pnpm',
      'package-lock.json': 'npm',
      'bun.lockb': 'bun',
      'poetry.lock': 'poetry',
      'Pipfile.lock': 'pipenv',
      'Cargo.lock': 'cargo',
      'go.sum': 'go-modules',
      'composer.lock': 'composer',
      'Gemfile.lock': 'bundler',
    };

    // Framework indicators
    this.frameworkIndicators = {
      'next.config.js': 'nextjs',
      'next.config.mjs': 'nextjs',
      'next.config.ts': 'nextjs',
      'nuxt.config.js': 'nuxtjs',
      'nuxt.config.ts': 'nuxtjs',
      'angular.json': 'angular',
      'vue.config.js': 'vue',
      'vite.config.js': 'vite',
      'vite.config.ts': 'vite',
      'svelte.config.js': 'svelte',
      'gatsby-config.js': 'gatsby',
      'remix.config.js': 'remix',
      'astro.config.mjs': 'astro',
      'webpack.config.js': 'webpack',
      'rollup.config.js': 'rollup',
      '.ember-cli': 'ember',
      'quasar.conf.js': 'quasar',
      'ionic.config.json': 'ionic',
      'expo.json': 'expo',
      'app.json': 'react-native',
      'metro.config.js': 'react-native',
    };
  }

  /**
   * Detect project types in a directory
   *
   * @param {string} dirPath - Absolute path to analyze
   * @returns {object} Detected project information
   */
  async detectProjectType(dirPath) {
    const result = {
      languages: [],
      frameworks: [],
      packageManagers: [],
      projectTypes: [], // Combined array for backward compatibility
      raw: {
        languages: new Set(),
        frameworks: new Set(),
        packageManagers: new Set(),
      },
    };

    try {
      // Check for language indicators
      for (const [file, language] of Object.entries(this.languageIndicators)) {
        if (await this.fileOrPatternExists(dirPath, file)) {
          result.raw.languages.add(language);
        }
      }

      // Check for package manager indicators
      for (const [file, manager] of Object.entries(this.packageManagerIndicators)) {
        if (await this.fileOrPatternExists(dirPath, file)) {
          result.raw.packageManagers.add(manager);
        }
      }

      // Check for framework indicators
      for (const [file, framework] of Object.entries(this.frameworkIndicators)) {
        if (await this.fileOrPatternExists(dirPath, file)) {
          result.raw.frameworks.add(framework);
        }
      }

      // Special case: Check package.json for framework clues
      await this.detectFromPackageJson(dirPath, result.raw);

      // Convert sets to arrays
      result.languages = Array.from(result.raw.languages);
      result.frameworks = Array.from(result.raw.frameworks);
      result.packageManagers = Array.from(result.raw.packageManagers);

      // Combined array for backward compatibility
      result.projectTypes = [
        ...result.languages,
        ...result.frameworks,
        ...result.packageManagers.map((pm) => `${result.languages[0]}-${pm}`),
      ].filter(Boolean);
    } catch (error) {
      console.error(`Error detecting project type: ${error.message}`);
    }

    return result;
  }

  /**
   * Check if a file or pattern exists in directory
   *
   * @param {string} dirPath - Directory to check in
   * @param {string} filePattern - File name or pattern to check
   * @returns {boolean} True if file/pattern exists
   */
  async fileOrPatternExists(dirPath, filePattern) {
    try {
      // Handle patterns like *.csproj
      if (filePattern.includes('*')) {
        const files = await fs.readdir(dirPath);
        const regex = new RegExp(filePattern.replace('*', '.*'));
        return files.some((file) => regex.test(file));
      }

      // Direct file check
      const filePath = path.join(dirPath, filePattern);
      return await fs.pathExists(filePath);
    } catch (error) {
      return false;
    }
  }

  /**
   * Detect frameworks from package.json dependencies
   *
   * @param {string} dirPath - Directory containing package.json
   * @param {object} raw - Raw result sets to update
   */
  async detectFromPackageJson(dirPath, raw) {
    const packageJsonPath = path.join(dirPath, 'package.json');

    try {
      if (await fs.pathExists(packageJsonPath)) {
        const packageJson = await fs.readJson(packageJsonPath);
        const allDeps = {
          ...packageJson.dependencies,
          ...packageJson.devDependencies,
        };

        // Framework detection from dependencies
        const frameworkDeps = {
          react: 'react',
          vue: 'vue',
          '@angular/core': 'angular',
          svelte: 'svelte',
          next: 'nextjs',
          nuxt: 'nuxtjs',
          gatsby: 'gatsby',
          '@remix-run/react': 'remix',
          astro: 'astro',
          express: 'express',
          fastify: 'fastify',
          koa: 'koa',
          hapi: 'hapi',
          nestjs: 'nestjs',
          '@nestjs/core': 'nestjs',
          electron: 'electron',
        };

        for (const [dep, framework] of Object.entries(frameworkDeps)) {
          if (dep in allDeps) {
            raw.frameworks.add(framework);
          }
        }

        // Detect TypeScript
        if ('typescript' in allDeps) {
          raw.languages.add('typescript');
        }
      }
    } catch (error) {
      // Ignore errors reading package.json
    }
  }

  /**
   * Get project name from configuration files
   *
   * @param {string} dirPath - Project directory
   * @returns {string} Project name or directory name
   */
  async getProjectName(dirPath) {
    // Try package.json first
    try {
      const packageJsonPath = path.join(dirPath, 'package.json');
      if (await fs.pathExists(packageJsonPath)) {
        const packageJson = await fs.readJson(packageJsonPath);
        if (packageJson.name) {
          return packageJson.name;
        }
      }
    } catch (error) {
      // Continue to other methods
    }

    // Try other config files
    const configFiles = [
      { file: 'Cargo.toml', parser: this.parseTomlName },
      { file: 'pyproject.toml', parser: this.parseTomlName },
      { file: 'composer.json', parser: this.parseJsonName },
    ];

    for (const { file, parser } of configFiles) {
      try {
        const configPath = path.join(dirPath, file);
        if (await fs.pathExists(configPath)) {
          const name = await parser.call(this, configPath);
          if (name) {
            return name;
          }
        }
      } catch (error) {
        // Continue to next
      }
    }

    // Default to directory name
    return path.basename(dirPath);
  }

  /**
   * Parse name from TOML file
   *
   * @param {string} filePath - Path to TOML file
   * @returns {string|null} Project name or null
   */
  async parseTomlName(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const match = content.match(/name\s*=\s*"([^"]+)"/);
      return match ? match[1] : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Parse name from JSON file
   *
   * @param {string} filePath - Path to JSON file
   * @returns {string|null} Project name or null
   */
  async parseJsonName(filePath) {
    try {
      const json = await fs.readJson(filePath);
      return json.name || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Determine primary language of the project
   *
   * @param {Array<string>} languages - Detected languages
   * @returns {string} Primary language
   */
  getPrimaryLanguage(languages) {
    // Priority order for primary language
    const priority = [
      'typescript',
      'node',
      'python',
      'rust',
      'go',
      'java',
      'csharp',
      'cpp',
      'ruby',
      'php',
    ];

    for (const lang of priority) {
      if (languages.includes(lang)) {
        return lang;
      }
    }

    return languages[0] || 'unknown';
  }
}

module.exports = ProjectDetector;
