const path = require('path');

const fs = require('fs-extra');
const chalk = require('chalk');

/**
 * DirectoryGenerator handles the creation of the complete directory structure
 * needed for the Claude Development workflow system.
 */
class DirectoryGenerator {
  /**
   * Creates the complete directory structure needed for the workflow
   * - .claude/ for Claude Code configuration and hooks
   * - scripts/ for workflow automation
   * - workflows/ for the main parallel development system
   * - shared/ for coordination between agents
   * - .linear-cache/ for offline Linear issue storage
   *
   * @param {string} targetDir - The target directory for installation
   * @returns {Promise<void>}
   */
  async generate(targetDir) {
    console.log(chalk.gray('  " Creating directory structure...'));

    const directories = [
      '.claude', // Claude Code configuration
      '.claude/hooks', // Hook scripts for Claude integration
      '.claude/commands', // Custom Claude commands
      '.claude/logs', // Hook execution logs
      '.claude/templates', // Template files for <PERSON>
      'scripts', // General automation scripts
      'workflows', // Workflow definitions
      'workflows/paralell-development-claude', // Main parallel workflow
      'workflows/paralell-development-claude/scripts', // Workflow-specific scripts
      'workflows/paralell-development-claude/ai_docs', // AI documentation
      'shared', // Shared resources between agents
      'shared/deployment-plans', // Agent deployment configurations
      'shared/coordination', // Inter-agent coordination files
      'shared/reports', // Progress and status reports
      '.linear-cache', // Cached Linear issues for offline work
    ];

    // Create each directory with proper error handling
    for (const dir of directories) {
      const fullPath = path.join(targetDir, dir);
      await fs.ensureDir(fullPath);
      console.log(chalk.gray(`    Created: ${dir}`));
    }
  }
}

module.exports = { DirectoryGenerator };
