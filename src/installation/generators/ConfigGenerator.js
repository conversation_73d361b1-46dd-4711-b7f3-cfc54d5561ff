const path = require('path');

const fs = require('fs-extra');
const chalk = require('chalk');

/**
 * ConfigGenerator handles the creation of all configuration files
 * needed for the Claude Development workflow system.
 */
class ConfigGenerator {
  /**
   * Generates all configuration files including:
   * - .env and .env.example
   * - .gitignore
   * - Claude settings
   * - package.json updates
   *
   * @param {string} targetDir - The target directory for installation
   * @param {Object} config - Configuration options
   * @returns {Promise<void>}
   */
  async generate(targetDir, config) {
    console.log(chalk.gray('  " Creating configuration files...'));

    // Create .env.example
    await this.createEnvExample(targetDir, config);

    // Create .env if Linear API key was provided
    if (config.linearApiKey) {
      await this.createEnvFile(targetDir, config);
    }

    // Create .gitignore with Claude Code specific entries
    await this.createGitignore(targetDir);

    // Create Claude Code settings.json
    await this.createClaudeSettings(targetDir, config);

    // Update or create package.json
    await this.updatePackageJson(targetDir, config);
  }

  /**
   * Creates the .env.example file with configuration template
   */
  async createEnvExample(targetDir, config) {
    const envExamplePath = path.join(targetDir, '.env.example');
    const envExampleContent = `# Parallel Claude Development Workflow Configuration

# Linear Integration
LINEAR_API_KEY=your_linear_api_key_here

# Project Configuration
PROJECT_NAME=${config.projectName}
WORKTREE_PATH=${config.workTreePath}

# Optional Configuration
CLAUDE_MODEL=claude-3-5-sonnet-20241022
EDITOR=cursor
`;

    await fs.writeFile(envExamplePath, envExampleContent);
    console.log(chalk.gray(`    Created: .env.example`));
  }

  /**
   * Creates the .env file with actual Linear API key if provided
   */
  async createEnvFile(targetDir, config) {
    const envPath = path.join(targetDir, '.env');
    const envContent = `# Parallel Claude Development Workflow Configuration

# Linear Integration
LINEAR_API_KEY=${config.linearApiKey}

# Project Configuration
PROJECT_NAME=${config.projectName}
WORKTREE_PATH=${config.workTreePath}

# Optional Configuration
CLAUDE_MODEL=claude-3-5-sonnet-20241022
EDITOR=cursor
`;

    await fs.writeFile(envPath, envContent);
    console.log(chalk.gray(`    Created: .env`));
  }

  /**
   * Creates a comprehensive .gitignore file
   */
  async createGitignore(targetDir) {
    const gitignorePath = path.join(targetDir, '.gitignore');
    const gitignoreContent = `# Dependencies
node_modules/
*.log

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cache directories
.linear-cache/
.next/
dist/
build/
__pycache__/
*.pyc
*.pyo
*.pyd

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db
`;

    await fs.writeFile(gitignorePath, gitignoreContent);
    console.log(chalk.gray(`    Created: .gitignore`));
  }

  /**
   * Creates Claude Code settings.json with project-specific configuration
   */
  async createClaudeSettings(targetDir, config) {
    const settingsPath = path.join(targetDir, '.claude/settings.json');

    // Build project-specific settings based on detected project type
    const projectSpecificSettings = this.getProjectSpecificSettings(
      config.projectType,
      config.packageManager,
      targetDir,
    );

    const settings = {
      permissions: {
        allow: [
          'Bash(mkdir:*)',
          'Bash(uv:*)',
          'Bash(find:*)',
          'Bash(mv:*)',
          'Bash(grep:*)',
          'Bash(npm:*)',
          'Bash(ls:*)',
          'Bash(cp:*)',
          'Write',
          'Edit',
          'Bash(chmod:*)',
          'Bash(touch:*)',
        ],
        deny: [],
      },
      hooks: {
        PreToolUse: [
          {
            matcher: '',
            hooks: [
              {
                type: 'command',
                command: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/pre_tool_use.py',
              },
            ],
          },
        ],
        PostToolUse: [
          {
            matcher: '',
            hooks: [
              {
                type: 'command',
                command: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/post_tool_use.py',
              },
            ],
          },
          {
            matcher: 'Write|Edit',
            hooks: [
              {
                type: 'command',
                command: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/import-organizer.py',
              },
              {
                type: 'command',
                command:
                  'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/auto_commit_on_changes.py',
              },
            ],
          },
          {
            matcher: 'Write|Edit',
            hooks: [
              {
                type: 'command',
                command: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/universal-linter.py',
              },
            ],
          },
        ],
        Notification: [
          {
            matcher: '',
            hooks: [
              {
                type: 'command',
                command:
                  'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/notification.py --notify',
              },
            ],
          },
        ],
        Stop: [
          {
            matcher: '',
            hooks: [
              {
                type: 'command',
                command: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/stop.py --chat',
              },
            ],
          },
        ],
        SubagentStop: [
          {
            matcher: '',
            hooks: [
              {
                type: 'command',
                command: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/subagent_stop.py',
              },
            ],
          },
        ],
      },
      ...projectSpecificSettings,
    };

    await fs.writeFile(settingsPath, JSON.stringify(settings, null, 2));
    console.log(chalk.gray(`    Created: .claude/settings.json`));
  }

  /**
   * Gets project-specific settings based on project type
   */
  getProjectSpecificSettings(projectType, packageManager, targetDir) {
    const hasTypeScript = fs.existsSync(path.join(targetDir, 'tsconfig.json'));

    const baseSettings = {
      projectType,
      packageManager,
      typescript: hasTypeScript,
    };

    switch (projectType) {
      case 'nextjs':
        return {
          ...baseSettings,
          framework: 'next',
          buildTool: 'next',
          validation: {
            typescript: hasTypeScript,
            typeCheck: hasTypeScript,
          },
        };

      case 'react':
        return {
          ...baseSettings,
          framework: 'react',
          buildTool: 'vite',
          testRunner: 'vitest',
          validation: {
            typescript: hasTypeScript,
            typeCheck: hasTypeScript,
          },
        };

      case 'nodejs':
        return {
          ...baseSettings,
          runtime: 'node',
          framework: 'express',
          validation: {
            typescript: hasTypeScript,
            typeCheck: hasTypeScript,
          },
        };

      case 'python':
        return {
          ...baseSettings,
          runtime: 'python',
          framework: 'flask',
          packageManager: 'pip',
        };

      case 'monorepo':
        const hasTurbo = fs.existsSync(path.join(targetDir, 'turbo.json'));
        const hasLerna = fs.existsSync(path.join(targetDir, 'lerna.json'));
        const hasRush = fs.existsSync(path.join(targetDir, 'rush.json'));

        return {
          ...baseSettings,
          workspaces: true,
          monorepoTool: this.determineMonorepoTool(hasTurbo, hasLerna, hasRush),
          validation: {
            typescript: hasTypeScript,
            typeCheck: hasTypeScript,
          },
        };

      case 'minimal':
        // Check if it's truly minimal or has some files
        const packageJsonPath = path.join(targetDir, 'package.json');
        const packageJson = fs.existsSync(packageJsonPath)
          ? JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
          : null;
        const hasMainFile =
          fs.existsSync(path.join(targetDir, 'index.js')) ||
          fs.existsSync(path.join(targetDir, 'main.js'));

        const isGeneric = !packageJson || (!packageJson.scripts && !hasMainFile);

        return {
          ...baseSettings,
          projectType: isGeneric ? 'generic' : 'javascript',
          minimal: true,
        };

      default:
        return baseSettings;
    }
  }

  /**
   * Determines the monorepo tool being used
   */
  determineMonorepoTool(hasTurbo, hasLerna, hasRush) {
    if (hasTurbo) {
      return 'turbo';
    }
    if (hasLerna) {
      return 'lerna';
    }
    if (hasRush) {
      return 'rush';
    }
    return 'npm';
  }

  /**
   * Updates or creates package.json with necessary scripts and dependencies
   */
  async updatePackageJson(targetDir, config) {
    const packageJsonPath = path.join(targetDir, 'package.json');
    let packageJson;

    if (await fs.pathExists(packageJsonPath)) {
      packageJson = await fs.readJson(packageJsonPath);
    } else {
      packageJson = {
        name: config.projectName,
        version: '1.0.0',
        description: 'Project using Parallel Claude Development Workflow',
        main: 'index.js',
        dependencies: {
          dotenv: '^16.6.1',
        },
        devDependencies: {},
        keywords: ['claude', 'parallel', 'development', 'workflow'],
        author: '',
        license: 'MIT',
      };
    }

    // Ensure scripts object exists
    if (!packageJson.scripts) {
      packageJson.scripts = {};
    }

    // Add framework-specific scripts based on project type
    const frameworkScripts = this.getFrameworkScripts(config.projectType, config);
    Object.assign(packageJson.scripts, frameworkScripts);

    // Add core parallel development scripts
    Object.assign(packageJson.scripts, {
      decompose: './scripts/python/decompose-parallel.py',
      'spawn-agents': './scripts/python/spawn-agents.py',
      'cache-issue': './scripts/python/cache-linear-issue.py',
      'changelog:update': 'node scripts/changelog/update-changelog.js',
      'changelog:auto': 'node scripts/changelog/update-changelog.js --auto',
      'changelog:manual': 'node scripts/changelog/update-changelog.js --manual',
      'changelog:preview': 'node scripts/changelog/update-changelog.js --auto --dry-run',
    });

    await fs.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log(chalk.gray(`    Updated: package.json`));
  }

  /**
   * Gets framework-specific scripts for package.json
   */
  getFrameworkScripts(projectType, config) {
    const baseScripts = {
      'claude:spawn': './scripts/python/spawn-agents.py',
      'claude:integrate': './scripts/python/integrate-parallel-work.py',
      'claude:monitor': './scripts/python/monitor-agents.py',
      'claude:validate': './scripts/python/validate-parallel-work.py',
      'claude:resolve': './scripts/python/resolve-conflicts.py',
    };

    const linearScripts = config.setupLinear
      ? {
          'claude:cache': './scripts/python/cache-linear-issue.py',
          'claude:decompose': './scripts/python/decompose-parallel.py',
        }
      : {};

    return { ...baseScripts, ...linearScripts };
  }
}

module.exports = { ConfigGenerator };
