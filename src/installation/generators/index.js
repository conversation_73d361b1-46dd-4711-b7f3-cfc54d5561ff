/**
 * Generator exports for the installation system
 *
 * This module exports all generator classes that are responsible for
 * creating different parts of the Claude Development workflow system.
 */

const { DirectoryGenerator } = require('./DirectoryGenerator');
const { ConfigGenerator } = require('./ConfigGenerator');
const { ScriptGenerator } = require('./ScriptGenerator');
const { HookGenerator } = require('./HookGenerator');
const { TemplateGenerator } = require('./TemplateGenerator');

module.exports = {
  DirectoryGenerator,
  ConfigGenerator,
  ScriptGenerator,
  HookGenerator,
  TemplateGenerator,
};
