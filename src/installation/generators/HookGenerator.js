const path = require('path');

const fs = require('fs-extra');
const chalk = require('chalk');

/**
 * HookGenerator handles the creation of Claude Code hooks
 * for the Claude Development workflow system.
 */
class HookGenerator {
  /**
   * Generates all Claude Code hooks needed for the workflow integration
   * These hooks integrate with Claude Code's lifecycle events
   *
   * @param {string} targetDir - The target directory for installation
   * @returns {Promise<void>}
   */
  async generate(targetDir) {
    const hooksDir = path.join(targetDir, '.claude/hooks');

    // Ensure hooks directory exists
    await fs.ensureDir(hooksDir);

    // Create hook files that the integration tests expect
    const hooks = [
      'pre_tool_use.py',
      'post_tool_use.py',
      'stop.py',
      'subagent_stop.py',
      'notification.py',
      'typescript-validator.py',
      'code-quality-reporter.py',
      'api-standards-checker.py',
    ];

    for (const hook of hooks) {
      await this.createHook(hooksDir, hook);
    }
  }

  /**
   * Creates an individual hook file with basic implementation
   *
   * @param {string} hooksDir - The hooks directory path
   * @param {string} hookName - The name of the hook file
   */
  async createHook(hooksDir, hookName) {
    const hookPath = path.join(hooksDir, hookName);
    const hookContent = this.getHookContent(hookName);

    await fs.writeFile(hookPath, hookContent);
    await fs.chmod(hookPath, '755');
    console.log(chalk.gray(`    Created: .claude/hooks/${hookName}`));
  }

  /**
   * Returns the appropriate content for each hook type
   *
   * @param {string} hookName - The name of the hook
   * @returns {string} The hook content
   */
  getHookContent(hookName) {
    // Extract the base name without extension for the description
    const baseName = hookName.replace('.py', '');
    const displayName = baseName.replace(/_/g, ' ').replace(/-/g, ' ');

    return `#!/usr/bin/env python3
# ${hookName} - Claude Code hook
"""
${displayName} hook for Claude Code integration.
This hook is triggered by Claude Code during the workflow lifecycle.
"""

import json
import sys
import os
from datetime import datetime

def main():
    """Main hook execution function."""
    try:
        # Read input from Claude Code if available
        if not sys.stdin.isatty():
            input_data = json.load(sys.stdin)
        else:
            input_data = {}
        
        # Log hook execution (for debugging)
        log_dir = os.path.join(os.path.dirname(__file__), '..', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        log_file = os.path.join(log_dir, f'{baseName}.log')
        with open(log_file, 'a') as f:
            f.write(f"[{datetime.now().isoformat()}] Hook executed\\n")
            f.write(f"Input: {json.dumps(input_data, indent=2)}\\n\\n")
        
        # Hook implementation goes here
        # Add your custom logic based on the hook type
        
        # Success output
        print(f"${hookName} executed successfully", file=sys.stdout)
        
    except Exception as e:
        # Error handling
        print(f"Hook error in ${hookName}: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
`;
  }

  /**
   * Creates framework-specific command templates
   * These are used by Claude Code for quick command execution
   *
   * @param {string} targetDir - The target directory
   * @param {Object} config - Configuration options
   */
  async createFrameworkCommands(targetDir, config) {
    const commandsDir = path.join(targetDir, '.claude/commands');

    // Ensure commands directory exists
    await fs.ensureDir(commandsDir);

    // Create framework-specific command templates
    const commands = this.getFrameworkCommands(config.projectType);

    // Add changelog command template
    commands['update-changelog.md'] = this.getChangelogCommandContent();

    for (const [filename, content] of Object.entries(commands)) {
      const commandPath = path.join(commandsDir, filename);
      await fs.writeFile(commandPath, content);
      console.log(chalk.gray(`    Created: .claude/commands/${filename}`));
    }
  }

  /**
   * Returns the changelog command template content
   */
  getChangelogCommandContent() {
    return `---
allowed-tools: Bash
description: Update project CHANGELOG.md using automated scripts
---

# Update Changelog

Simple command to update CHANGELOG.md using the automated changelog scripts. Supports automatic git analysis or manual entry modes.

## Quick Usage

\`\`\`bash
# Auto-generate from git commits
npm run changelog:auto

# Manual entry mode
npm run changelog:manual

# Preview changes without saving
npm run changelog:preview

# Update with specific version
npm run changelog:update 1.5.0 --auto
\`\`\`

## Command Options

**Auto Mode** (Default):
\`\`\`bash
npm run changelog:auto
\`\`\`
- Analyzes git commits since last release
- Automatically categorizes changes
- Determines semantic version bump

**Manual Mode**:
\`\`\`bash
npm run changelog:manual
\`\`\`
- Interactive prompts for each category
- Full control over changelog entries
- Guided entry process

**Preview Mode**:
\`\`\`bash
npm run changelog:preview
\`\`\`
- Shows what would be added
- No file modifications
- Safe to run anytime

**Custom Version**:
\`\`\`bash
npm run changelog:update 2.1.0 --auto
npm run changelog:update 1.0.3 --manual --verbose
\`\`\`

## Workflow

1. Run changelog command
2. Review generated/entered changes
3. Commit the updated CHANGELOG.md:

\`\`\`bash
git add CHANGELOG.md
git commit -m "docs: update changelog for v1.5.0"
\`\`\`

## Available Scripts

- \`scripts/changelog/update-changelog.js\` - Main automation script
- \`scripts/changelog/utils.js\` - Helper functions
- Full documentation: \`scripts/changelog/README.md\`
`;
  }

  /**
   * Gets framework-specific command templates
   */
  getFrameworkCommands(projectType) {
    const commonCommands = {
      'component.md': `# Component Template

Create a new component with the following structure:

\`\`\`typescript
interface ComponentProps {
  // Define props here
}

export default function Component(props: ComponentProps) {
  return (
    <div>
      {/* Component implementation */}
    </div>
  );
}
\`\`\`
`,
      'test.md': `# Test Template

Create comprehensive tests:

\`\`\`typescript
describe('Component', () => {
  it('should render correctly', () => {
    // Test implementation
  });
});
\`\`\`
`,
    };

    switch (projectType) {
      case 'nextjs':
        return {
          ...commonCommands,
          'next-page-generator.md': `# Next.js Page Generator

\`\`\`typescript
export default function Page() {
  return (
    <div>
      <h1>Page Title</h1>
    </div>
  );
}
\`\`\`
`,
          'next-api-route.md': `# API Route Template

\`\`\`typescript
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  return NextResponse.json({ message: 'Hello World' });
}
\`\`\`
`,
        };

      case 'react':
        return {
          ...commonCommands,
          'react-component.md': `# React Component Template

\`\`\`typescript
interface ComponentProps {
  // Define props here
}

export default function Component(props: ComponentProps) {
  return (
    <div>
      {/* Component implementation */}
    </div>
  );
}
\`\`\`
`,
          'react-hook.md': `# React Hook Template

\`\`\`typescript
import { useState, useEffect } from 'react';

export function useCustomHook() {
  const [state, setState] = useState();
  
  useEffect(() => {
    // Effect logic
  }, []);
  
  return { state, setState };
}
\`\`\`
`,
        };

      case 'nodejs':
        return {
          ...commonCommands,
          'api-endpoint.md': `# Express Endpoint Template

\`\`\`typescript
import { Request, Response } from 'express';

export async function handler(req: Request, res: Response) {
  try {
    // Implementation
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}
\`\`\`
`,
          'middleware.md': `# Middleware Template

\`\`\`typescript
export function middleware(req: Request, res: Response, next: Function) {
  // Middleware logic
  next();
}
\`\`\`
`,
        };

      case 'python':
        return {
          'endpoint.md': `# Flask Endpoint Template

\`\`\`python
from flask import jsonify, request

@app.route('/api/endpoint', methods=['GET', 'POST'])
def endpoint():
    if request.method == 'GET':
        return jsonify({'message': 'Hello World'})
    
    data = request.get_json()
    return jsonify({'received': data})
\`\`\`
`,
          'model.md': `# Model Template

\`\`\`python
from dataclasses import dataclass
from typing import Optional

@dataclass
class Model:
    id: Optional[int] = None
    name: str = ""
\`\`\`
`,
        };

      default:
        return commonCommands;
    }
  }
}

module.exports = { HookGenerator };
