const path = require('path');

const fs = require('fs-extra');
const chalk = require('chalk');

/**
 * TemplateGenerator handles copying workflow template files
 * from the package to the target directory.
 */
class TemplateGenerator {
  /**
   * Copies workflow template files from the package to the target directory
   * - Python scripts for core workflow functionality
   * - Shell wrapper scripts for compatibility
   * - Conditional Linear integration scripts
   *
   * @param {string} targetDir - The target directory for installation
   * @param {Object} config - Configuration options
   * @returns {Promise<void>}
   */
  async generate(targetDir, config) {
    // Base Python scripts (always installed) - core workflow functionality
    const baseTemplates = [
      'scripts/python/spawn-agents.py', // Create agent worktrees
      'scripts/python/monitor-agents.py', // Monitor agent progress
      'scripts/python/agent-commit.py', // Handle agent commits
      'scripts/python/validate-parallel-work.py', // Validate parallel work
      'scripts/python/integrate-parallel-work.py', // Merge agent work
      'scripts/python/resolve-conflicts.py', // Handle merge conflicts
      'scripts/python/intelligent-agent-generator.py', // Generate new agents
      'scripts/python/security-check.py', // Security and secrets scanning
      'scripts/python/changelog/update-changelog.py', // Update changelog
      'scripts/python/changelog/utils.py', // Changelog utilities
      'scripts/python/changelog/README.md', // Changelog documentation
      'scripts/python/test-locally.py', // Test locally before deployment
    ];

    // Linear-specific Python scripts (only if Linear integration enabled)
    const linearTemplates = config.setupLinear
      ? [
          'scripts/python/cache-linear-issue.py', // Cache Linear issues offline
          'scripts/python/decompose-parallel.py', // Break down Linear issues
        ]
      : [];

    // Additional Python scripts (always installed)
    const additionalTemplates = [
      'scripts/python/deploy.py', // Deployment script
      'scripts/python/prepublish.py', // Pre-publish validation
      'scripts/python/postpublish.py', // Post-publish tasks
    ];

    const templateFiles = [...baseTemplates, ...linearTemplates, ...additionalTemplates];

    // Copy each template file from package to target directory
    for (const templateFile of templateFiles) {
      await this.copyTemplate(templateFile, targetDir);
    }
  }

  /**
   * Copies a single template file from the package to the target directory
   *
   * @param {string} templateFile - The relative path of the template file
   * @param {string} targetDir - The target directory
   */
  async copyTemplate(templateFile, targetDir) {
    // Source: file in the installed package
    const sourcePath = path.join(__dirname, '..', '..', '..', templateFile);
    // Target: file in user's project
    const targetPath = path.join(targetDir, templateFile);

    // Ensure target directory exists before copying
    await fs.ensureDir(path.dirname(targetPath));

    if (await fs.pathExists(sourcePath)) {
      await fs.copy(sourcePath, targetPath);

      // Make Python scripts executable (important for direct execution)
      if (templateFile.endsWith('.py')) {
        await fs.chmod(targetPath, 0o755);
      }
    } else {
      console.warn(chalk.yellow(`�  Template not found: ${sourcePath}`));
    }
  }

  /**
   * Creates example files for the workflow
   * These demonstrate the expected structure and format
   *
   * @param {string} targetDir - The target directory
   * @param {Object} config - Configuration options
   */
  async createExampleFiles(targetDir, config) {
    console.log(chalk.gray('  " Creating example files...'));

    // Create example Linear issue cache
    await this.createExampleIssueCache(targetDir);

    // Create example deployment plan
    await this.createExampleDeploymentPlan(targetDir);
  }

  /**
   * Creates an example Linear issue cache file
   */
  async createExampleIssueCache(targetDir) {
    const exampleIssuePath = path.join(targetDir, '.linear-cache', 'EXAMPLE-123.json');
    const exampleIssue = {
      id: 'example-123',
      identifier: 'EXAMPLE-123',
      title: 'Example: Implement user authentication system',
      description:
        'This is an example Linear issue showing how to structure requirements for parallel development.\n\n1. Create login/signup forms\n2. Implement JWT authentication\n3. Add password reset functionality\n4. Create user profile management\n5. Add role-based access control',
      priority: 1,
      priorityLabel: 'High',
      state: { name: 'Todo' },
      assignee: { name: 'Developer', email: '<EMAIL>' },
      team: { name: 'Engineering' },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await fs.writeFile(exampleIssuePath, JSON.stringify(exampleIssue, null, 2));
    console.log(chalk.gray(`    Created: example issue cache`));
  }

  /**
   * Creates an example deployment plan file
   */
  async createExampleDeploymentPlan(targetDir) {
    const examplePlanPath = path.join(
      targetDir,
      'shared',
      'deployment-plans',
      'example-123-deployment-plan.json',
    );
    const examplePlan = {
      taskId: 'EXAMPLE-123',
      taskTitle: 'Example: Implement user authentication system',
      parallelAgents: [
        {
          agentId: 'frontend_forms_agent',
          agentRole: 'Frontend & UI: Login/signup forms and user interface',
          focusArea: 'Frontend & UI',
          canStartImmediately: true,
          filesToCreate: ['components/LoginForm.tsx', 'components/SignupForm.tsx'],
          filesToModify: ['pages/login.tsx', 'pages/signup.tsx'],
          estimatedTime: 30,
        },
        {
          agentId: 'backend_auth_agent',
          agentRole: 'Backend & API: JWT authentication and user management',
          focusArea: 'Backend & API',
          canStartImmediately: true,
          filesToCreate: ['lib/auth.ts', 'pages/api/auth/[...nextauth].ts'],
          filesToModify: ['lib/database.ts'],
          estimatedTime: 45,
        },
      ],
      estimatedTotalTime: '45 minutes',
      parallelismFactor: '1.5x faster than sequential',
      integrationPlan: {
        mergeOrder: ['backend_auth_agent', 'frontend_forms_agent'],
        validationSteps: ['Run tests', 'Integration testing', 'E2E validation'],
        estimatedIntegrationTime: '15 minutes',
      },
    };

    await fs.writeFile(examplePlanPath, JSON.stringify(examplePlan, null, 2));
    console.log(chalk.gray(`    Created: example deployment plan`));
  }

  /**
   * Sets up environment variables script
   *
   * @param {string} targetDir - The target directory
   * @param {Object} config - Configuration options
   */
  async setupEnvironmentVariables(targetDir, config) {
    console.log(chalk.gray('  " Setting up environment variables...'));

    // Create shell script to source environment
    const envScriptPath = path.join(
      targetDir,
      'workflows',
      'paralell-development-claude',
      'env.sh',
    );
    const envScriptContent = `#!/bin/bash

# Source project environment variables
if [ -f "../../.env" ]; then
    export $(cat ../../.env | xargs)
    echo "Environment variables loaded from .env"
fi

# Set default values if not provided
export PROJECT_NAME=\${PROJECT_NAME:-"${config.projectName}"}
export WORKTREE_PATH=\${WORKTREE_PATH:-"${config.workTreePath}"}
export CLAUDE_MODEL=\${CLAUDE_MODEL:-"claude-3-5-sonnet-20241022"}
export EDITOR=\${EDITOR:-"cursor"}

echo "Parallel Claude Development Workflow environment ready"
`;

    await fs.writeFile(envScriptPath, envScriptContent);
    await fs.chmod(envScriptPath, '755');
    console.log(chalk.gray(`    Created: env.sh`));
  }
}

module.exports = { TemplateGenerator };
