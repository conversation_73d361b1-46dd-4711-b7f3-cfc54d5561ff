const path = require('path');

const fs = require('fs-extra');
const chalk = require('chalk');

/**
 * ScriptGenerator handles the creation of essential scripts
 * for the Claude Development workflow system.
 */
class ScriptGenerator {
  /**
   * Creates essential scripts with embedded content
   * Used as fallback when template files aren't available
   * These are basic implementations that can be enhanced later
   *
   * @param {string} targetDir - The target directory for installation
   * @returns {Promise<void>}
   */
  async generate(targetDir) {
    const scriptsDir = path.join(targetDir, 'workflows', 'paralell-development-claude', 'scripts');

    // Ensure scripts directory exists
    await fs.ensureDir(scriptsDir);

    // Create the essential scripts
    await this.createCacheLinearIssueScript(scriptsDir);
    await this.createDecomposeParallelScript(scriptsDir);
    await this.createSpawnAgentsScript(scriptsDir);
  }

  /**
   * Creates the cache-linear-issue.sh script
   */
  async createCacheLinearIssueScript(scriptsDir) {
    const content = `#!/bin/bash
# Cache Linear issue script for Claude Code integration
ISSUE_ID=\${1:-""}
if [ -z "$ISSUE_ID" ]; then
  echo "Usage: $0 <ISSUE_ID>"
  exit 1
fi

echo "Caching Linear issue: $ISSUE_ID"
# Linear API integration would go here
echo "Issue cached successfully"
`;

    const scriptPath = path.join(scriptsDir, 'cache-linear-issue.sh');
    await fs.writeFile(scriptPath, content);
    await fs.chmod(scriptPath, '755');
    console.log(chalk.gray(`    Created: scripts/cache-linear-issue.sh`));
  }

  /**
   * Creates the decompose-parallel.py script
   */
  async createDecomposeParallelScript(scriptsDir) {
    const content = `#!/usr/bin/env python3
"""
Parallel decomposition script for Claude Code integration
Breaks down Linear issues into parallel development tasks
"""
import sys
import json
import os
from pathlib import Path

def main():
    print("Decomposing task for parallel development...")
    
    # Get task ID from command line argument
    task_id = sys.argv[1] if len(sys.argv) > 1 else "default"
    
    # Task decomposition logic would go here
    deployment_plan = {
        "taskId": task_id,
        "parallelAgents": [],
        "estimatedTime": "30 minutes",
        "created": "auto-generated fallback"
    }
    
    print(f"Deployment plan created: {json.dumps(deployment_plan, indent=2)}")
    
    # Save deployment plan to shared directory
    shared_dir = Path("shared/deployment-plans")
    shared_dir.mkdir(parents=True, exist_ok=True)
    
    plan_file = shared_dir / f"{task_id}-deployment-plan.json"
    with open(plan_file, 'w') as f:
        json.dump(deployment_plan, f, indent=2)
    
    print(f"Deployment plan saved to: {plan_file}")

if __name__ == "__main__":
    main()
`;

    const scriptPath = path.join(scriptsDir, 'decompose-parallel.py');
    await fs.writeFile(scriptPath, content);
    await fs.chmod(scriptPath, '755');
    console.log(chalk.gray(`    Created: scripts/decompose-parallel.py`));
  }

  /**
   * Creates the spawn-agents.py script
   */
  async createSpawnAgentsScript(scriptsDir) {
    const content = `#!/usr/bin/env python3
"""
Spawn parallel agents script for Claude Code integration
Creates git worktrees and sets up parallel development environments
"""
import sys
import json
import os
import subprocess
from pathlib import Path

def main():
    print("Spawning parallel development agents...")
    
    # Get deployment plan from command line argument
    deployment_plan_file = sys.argv[1] if len(sys.argv) > 1 else "deployment-plan.json"
    
    if not os.path.exists(deployment_plan_file):
        print(f"Deployment plan not found: {deployment_plan_file}")
        sys.exit(1)
    
    try:
        with open(deployment_plan_file, 'r') as f:
            deployment_plan = json.load(f)
        
        print(f"Using deployment plan: {deployment_plan_file}")
        print(f"Task ID: {deployment_plan.get('taskId', 'unknown')}")
        
        # Agent spawning logic would go here
        parallel_agents = deployment_plan.get('parallelAgents', [])
        
        if not parallel_agents:
            print("No parallel agents defined in deployment plan")
            return
        
        print(f"Found {len(parallel_agents)} agents to spawn")
        for agent in parallel_agents:
            agent_id = agent.get('agentId', 'unknown')
            print(f"  - {agent_id}: {agent.get('agentRole', 'No role defined')}")
        
        print("Agents spawned successfully (placeholder implementation)")
        
    except json.JSONDecodeError as e:
        print(f"Error parsing deployment plan: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error spawning agents: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
`;

    const scriptPath = path.join(scriptsDir, 'spawn-agents.py');
    await fs.writeFile(scriptPath, content);
    await fs.chmod(scriptPath, '755');
    console.log(chalk.gray(`    Created: scripts/spawn-agents.py`));
  }

  /**
   * Sets up scripts and permissions for all script files
   * This method can be called separately to ensure all scripts have proper permissions
   */
  async setupPermissions(targetDir) {
    console.log(chalk.gray('  " Setting up scripts and permissions...'));

    const scriptsDir = path.join(targetDir, 'workflows', 'paralell-development-claude', 'scripts');

    try {
      const scriptFiles = await fs.readdir(scriptsDir);
      const shellScripts = scriptFiles.filter((file) => file.endsWith('.sh'));
      const pythonScripts = scriptFiles.filter((file) => file.endsWith('.py'));

      // Make shell scripts executable
      for (const script of shellScripts) {
        const scriptPath = path.join(scriptsDir, script);
        await fs.chmod(scriptPath, '755');
        console.log(chalk.gray(`    Made executable: ${script}`));
      }

      // Make Python scripts executable
      for (const script of pythonScripts) {
        const scriptPath = path.join(scriptsDir, script);
        await fs.chmod(scriptPath, '755');
        console.log(chalk.gray(`    Made executable: ${script}`));
      }

      // Also make the main executable files executable
      const mainScripts = ['intelligent-agent-generator.js'];
      for (const script of mainScripts) {
        const scriptPath = path.join(scriptsDir, script);
        if (await fs.pathExists(scriptPath)) {
          await fs.chmod(scriptPath, '755');
          console.log(chalk.gray(`    Made executable: ${script}`));
        }
      }

      // Make changelog scripts executable
      const changelogScripts = ['changelog/update-changelog.js', 'changelog/utils.js'];
      for (const script of changelogScripts) {
        const scriptPath = path.join(scriptsDir, script);
        if (await fs.pathExists(scriptPath)) {
          await fs.chmod(scriptPath, '755');
          console.log(chalk.gray(`    Made executable: ${script}`));
        }
      }
    } catch (error) {
      console.log(chalk.yellow(`    Warning: Could not set script permissions: ${error.message}`));
    }
  }
}

module.exports = { ScriptGenerator };
