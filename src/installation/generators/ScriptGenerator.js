const path = require('path');

const fs = require('fs-extra');
const chalk = require('chalk');

/**
 * ScriptGenerator handles the creation of essential scripts
 * for the Claude Development workflow system.
 */
class ScriptGenerator {
  /**
   * Creates essential scripts with embedded content
   * Used as fallback when template files aren't available
   * These are basic implementations that can be enhanced later
   *
   * @param {string} targetDir - The target directory for installation
   * @returns {Promise<void>}
   */
  async generate(targetDir) {
    const scriptsDir = path.join(targetDir, 'workflows', 'paralell-development-claude', 'scripts');

    // Ensure scripts directory exists
    await fs.ensureDir(scriptsDir);

    // Create the essential scripts
    await this.createCacheLinearIssueScript(scriptsDir);
    await this.createDecomposeParallelScript(scriptsDir);
    await this.createSpawnAgentsScript(scriptsDir);
  }

  /**
   * Creates the cache-linear-issue.sh script
   */
  async createCacheLinearIssueScript(scriptsDir) {
    const content = `#!/bin/bash
# Cache Linear issue script for Claude Code integration
ISSUE_ID=\${1:-""}
if [ -z "$ISSUE_ID" ]; then
  echo "Usage: $0 <ISSUE_ID>"
  exit 1
fi

echo "Caching Linear issue: $ISSUE_ID"
# Linear API integration would go here
echo "Issue cached successfully"
`;

    const scriptPath = path.join(scriptsDir, 'cache-linear-issue.sh');
    await fs.writeFile(scriptPath, content);
    await fs.chmod(scriptPath, '755');
    console.log(chalk.gray(`    Created: scripts/cache-linear-issue.sh`));
  }

  /**
   * Creates the decompose-parallel.cjs script
   */
  async createDecomposeParallelScript(scriptsDir) {
    const content = `#!/usr/bin/env node
// Parallel decomposition script for Claude Code
const fs = require('fs');
const path = require('path');

console.log('Decomposing task for parallel development...');

// Task decomposition logic would go here
const deploymentPlan = {
  taskId: process.argv[2] || 'default',
  parallelAgents: [],
  estimatedTime: '30 minutes'
};

console.log('Deployment plan created:', deploymentPlan);
`;

    const scriptPath = path.join(scriptsDir, 'decompose-parallel.cjs');
    await fs.writeFile(scriptPath, content);
    await fs.chmod(scriptPath, '755');
    console.log(chalk.gray(`    Created: scripts/decompose-parallel.cjs`));
  }

  /**
   * Creates the spawn-agents.sh script
   */
  async createSpawnAgentsScript(scriptsDir) {
    const content = `#!/bin/bash
# Spawn parallel agents script for Claude Code
echo "Spawning parallel development agents..."

# Agent spawning logic would go here
DEPLOYMENT_PLAN=\${1:-"deployment-plan.json"}

if [ -f "$DEPLOYMENT_PLAN" ]; then
  echo "Using deployment plan: $DEPLOYMENT_PLAN"
  echo "Agents spawned successfully"
else
  echo "Deployment plan not found: $DEPLOYMENT_PLAN"
  exit 1
fi
`;

    const scriptPath = path.join(scriptsDir, 'spawn-agents.sh');
    await fs.writeFile(scriptPath, content);
    await fs.chmod(scriptPath, '755');
    console.log(chalk.gray(`    Created: scripts/spawn-agents.sh`));
  }

  /**
   * Sets up scripts and permissions for all script files
   * This method can be called separately to ensure all scripts have proper permissions
   */
  async setupPermissions(targetDir) {
    console.log(chalk.gray('  " Setting up scripts and permissions...'));

    const scriptsDir = path.join(targetDir, 'workflows', 'paralell-development-claude', 'scripts');

    try {
      const scriptFiles = await fs.readdir(scriptsDir);
      const shellScripts = scriptFiles.filter((file) => file.endsWith('.sh'));

      for (const script of shellScripts) {
        const scriptPath = path.join(scriptsDir, script);

        // Make script executable
        await fs.chmod(scriptPath, '755');
        console.log(chalk.gray(`    Made executable: ${script}`));
      }

      // Also make the main executable files executable
      const mainScripts = ['decompose-parallel.cjs', 'intelligent-agent-generator.js'];
      for (const script of mainScripts) {
        const scriptPath = path.join(scriptsDir, script);
        if (await fs.pathExists(scriptPath)) {
          await fs.chmod(scriptPath, '755');
          console.log(chalk.gray(`    Made executable: ${script}`));
        }
      }

      // Make changelog scripts executable
      const changelogScripts = ['changelog/update-changelog.js', 'changelog/utils.js'];
      for (const script of changelogScripts) {
        const scriptPath = path.join(scriptsDir, script);
        if (await fs.pathExists(scriptPath)) {
          await fs.chmod(scriptPath, '755');
          console.log(chalk.gray(`    Made executable: ${script}`));
        }
      }
    } catch (error) {
      console.log(chalk.yellow(`    Warning: Could not set script permissions: ${error.message}`));
    }
  }
}

module.exports = { ScriptGenerator };
