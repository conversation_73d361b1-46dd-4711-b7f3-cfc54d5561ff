const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const chalk = require('chalk');
const fs = require('fs-extra');

const { InstallUtils } = require('./install-utils');
const {
  DirectoryGenerator,
  ConfigGenerator,
  ScriptGenerator,
  HookGenerator,
  TemplateGenerator,
} = require('./installation/generators');

const execAsync = promisify(exec);

/**
 * InstallSteps class handles the step-by-step installation process
 * for the Claude Development workflow system
 */
class InstallSteps {
  constructor() {
    this.utils = new InstallUtils();

    // Initialize generators
    this.directoryGenerator = new DirectoryGenerator();
    this.configGenerator = new ConfigGenerator();
    this.scriptGenerator = new ScriptGenerator();
    this.hookGenerator = new HookGenerator();
    this.templateGenerator = new TemplateGenerator();
  }

  /**
   * Validates the target directory for installation
   * - Creates directory if it doesn't exist
   * - Checks for existing workflows to prevent conflicts
   * - Validates write permissions
   */
  async validateTargetDirectory(targetDir, options) {
    console.log(chalk.gray('  • Validating target directory...'));

    const exists = await fs.pathExists(targetDir);

    if (!exists) {
      // Create directory if it doesn't exist
      await fs.ensureDir(targetDir);
      console.log(chalk.gray(`    Created directory: ${targetDir}`));
    } else {
      // Check if directory is empty or has existing workflow
      // Get list of all files/folders in the target directory
      const files = await fs.readdir(targetDir);

      // Look for existing Claude workflow installations
      const hasClaudeWorkflow = files.includes('.claude');
      const hasWorkflowDir =
        files.includes('workflows') &&
        (await fs.pathExists(path.join(targetDir, 'workflows', 'paralell-development-claude')));

      // Prevent overwriting existing installations unless forced
      if ((hasClaudeWorkflow || hasWorkflowDir) && !options.force && !options.update) {
        throw new Error(
          `Directory ${targetDir} already contains a workflow. Use --force to overwrite.`,
        );
      }

      // Warn about non-empty directories (unless forced)
      if (files.length > 0 && !options.force) {
        const isEmpty = await this.utils.isDirectoryEmpty(targetDir);
        if (!isEmpty) {
          console.log(chalk.yellow(`    Warning: Directory ${targetDir} is not empty`));
        }
      }
    }

    // Validate write permissions - critical for installation success
    try {
      await fs.access(targetDir, fs.constants.W_OK);
    } catch (error) {
      throw new Error(`No write permission to directory: ${targetDir}`);
    }
  }

  /**
   * Validates that required system dependencies are available
   * - Node.js (required for running scripts)
   * - Git (required for worktree management)
   * - Claude Code (optional but recommended)
   * - npm (required for package management)
   */
  async validateEnvironment() {
    console.log(chalk.gray('  • Validating environment dependencies...'));

    const dependencies = [
      { name: 'Node.js', command: 'node --version', required: true },
      { name: 'Git', command: 'git --version', required: true },
      { name: 'Claude Code', command: 'claude --version', required: false },
      { name: 'npm', command: 'npm --version', required: true },
    ];

    const results = [];

    // Check each dependency by trying to run its version command
    for (const dep of dependencies) {
      try {
        const { stdout } = await execAsync(dep.command);
        results.push({
          name: dep.name,
          version: stdout.trim(),
          available: true,
          required: dep.required,
        });
        console.log(chalk.gray(`    ✓ ${dep.name}: ${stdout.trim()}`));
      } catch (error) {
        results.push({
          name: dep.name,
          version: null,
          available: false,
          required: dep.required,
        });

        if (dep.required) {
          console.log(chalk.red(`    ✗ ${dep.name}: Not found (required)`));
        } else {
          console.log(chalk.yellow(`    ⚠ ${dep.name}: Not found (optional)`));
        }
      }
    }

    // Fail installation if any required dependencies are missing
    const missingRequired = results.filter((r) => r.required && !r.available);

    if (missingRequired.length > 0) {
      console.log('');
      console.log(chalk.red('Missing required dependencies:'));
      missingRequired.forEach((dep) => {
        console.log(chalk.red(`  - ${dep.name}`));
      });
      console.log('');
      console.log(chalk.blue('Installation instructions:'));
      console.log('  Node.js: https://nodejs.org/');
      console.log('  Git: https://git-scm.com/');
      console.log('  Claude Code: https://claude.ai/code');
      throw new Error('Missing required dependencies');
    }

    return results;
  }

  /**
   * Creates the complete directory structure needed for the workflow
   * - .claude/ for Claude Code configuration and hooks
   * - scripts/ for workflow automation
   * - workflows/ for the main parallel development system
   * - shared/ for coordination between agents
   * - .linear-cache/ for offline Linear issue storage
   */
  async createDirectoryStructure(targetDir) {
    return await this.directoryGenerator.generate(targetDir);
  }

  /**
   * Copies workflow template files from the package to the target directory
   * - Python scripts for core workflow functionality
   * - Shell wrapper scripts for compatibility
   * - Conditional Linear integration scripts
   */
  async copyWorkflowTemplates(targetDir, config) {
    return await this.templateGenerator.generate(targetDir, config);
  }

  /**
   * Creates essential scripts with embedded content
   * Used as fallback when template files aren't available
   * These are basic implementations that can be enhanced later
   */
  async createEssentialScripts(targetDir) {
    return await this.scriptGenerator.generate(targetDir);
  }

  async setupScriptsAndPermissions(targetDir) {
    return await this.scriptGenerator.setupPermissions(targetDir);
  }

  async createConfigurationFiles(targetDir, config) {
    // Generate all configuration files using the ConfigGenerator
    await this.configGenerator.generate(targetDir, config);

    // Install Claude Code hooks using the HookGenerator
    await this.installClaudeHooks(targetDir);

    // Create framework-specific command templates using the HookGenerator
    await this.createFrameworkCommands(targetDir, config);
  }

  async createGitignore(targetDir) {
    const gitignorePath = path.join(targetDir, '.gitignore');
    const gitignoreContent = `# Dependencies
node_modules/
*.log

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cache directories
.linear-cache/
.next/
dist/
build/
__pycache__/
*.pyc
*.pyo
*.pyd

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db
`;

    await fs.writeFile(gitignorePath, gitignoreContent);
    console.log(chalk.gray(`    Created: .gitignore`));
  }

  async createClaudeSettings(targetDir, config) {
    const settingsPath = path.join(targetDir, '.claude/settings.json');

    // Build project-specific settings based on detected project type
    const projectSpecificSettings = this.getProjectSpecificSettings(
      config.projectType,
      config.packageManager,
      targetDir,
    );

    const settings = {
      permissions: {
        allow: [
          'Bash(mkdir:*)',
          'Bash(uv:*)',
          'Bash(find:*)',
          'Bash(mv:*)',
          'Bash(grep:*)',
          'Bash(npm:*)',
          'Bash(ls:*)',
          'Bash(cp:*)',
          'Write',
          'Edit',
          'Bash(chmod:*)',
          'Bash(touch:*)',
        ],
        deny: [],
      },
      hooks: {
        PreToolUse: [
          {
            matcher: '',
            hooks: [
              {
                type: 'command',
                command: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/pre_tool_use.py',
              },
            ],
          },
        ],
        PostToolUse: [
          {
            matcher: '',
            hooks: [
              {
                type: 'command',
                command: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/post_tool_use.py',
              },
            ],
          },
          {
            matcher: 'Write|Edit',
            hooks: [
              {
                type: 'command',
                command: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/import-organizer.py',
              },
              {
                type: 'command',
                command:
                  'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/auto_commit_on_changes.py',
              },
            ],
          },
          {
            matcher: 'Write|Edit',
            hooks: [
              {
                type: 'command',
                command: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/universal-linter.py',
              },
            ],
          },
        ],
        Notification: [
          {
            matcher: '',
            hooks: [
              {
                type: 'command',
                command:
                  'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/notification.py --notify',
              },
            ],
          },
        ],
        Stop: [
          {
            matcher: '',
            hooks: [
              {
                type: 'command',
                command: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/stop.py --chat',
              },
            ],
          },
        ],
        SubagentStop: [
          {
            matcher: '',
            hooks: [
              {
                type: 'command',
                command: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/subagent_stop.py',
              },
            ],
          },
        ],
      },
      ...projectSpecificSettings,
    };

    await fs.writeFile(settingsPath, JSON.stringify(settings, null, 2));
    console.log(chalk.gray(`    Created: .claude/settings.json`));
  }

  getProjectSpecificSettings(projectType, packageManager, targetDir) {
    const fs = require('fs');
    const hasTypeScript = fs.existsSync(path.join(targetDir, 'tsconfig.json'));

    const baseSettings = {
      projectType,
      packageManager,
      typescript: hasTypeScript,
    };

    switch (projectType) {
      case 'nextjs':
        return {
          ...baseSettings,
          framework: 'next',
          buildTool: 'next',
          validation: {
            typescript: hasTypeScript,
            typeCheck: hasTypeScript,
          },
        };

      case 'react':
        return {
          ...baseSettings,
          framework: 'react',
          buildTool: 'vite',
          testRunner: 'vitest',
          validation: {
            typescript: hasTypeScript,
            typeCheck: hasTypeScript,
          },
        };

      case 'nodejs':
        return {
          ...baseSettings,
          runtime: 'node',
          framework: 'express',
          validation: {
            typescript: hasTypeScript,
            typeCheck: hasTypeScript,
          },
        };

      case 'python':
        return {
          ...baseSettings,
          runtime: 'python',
          framework: 'flask',
          packageManager: 'pip',
        };

      case 'monorepo':
        const hasTurbo = fs.existsSync(path.join(targetDir, 'turbo.json'));
        const hasLerna = fs.existsSync(path.join(targetDir, 'lerna.json'));
        const hasRush = fs.existsSync(path.join(targetDir, 'rush.json'));

        return {
          ...baseSettings,
          workspaces: true,
          monorepoTool: this.determineMonorepoTool(hasTurbo, hasLerna, hasRush),
          validation: {
            typescript: hasTypeScript,
            typeCheck: hasTypeScript,
          },
        };

      case 'minimal':
        // Check if it's truly minimal or has some files
        const packageJsonPath = path.join(targetDir, 'package.json');
        const packageJson = fs.existsSync(packageJsonPath)
          ? JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
          : null;
        const hasMainFile =
          fs.existsSync(path.join(targetDir, 'index.js')) ||
          fs.existsSync(path.join(targetDir, 'main.js'));

        const isGeneric = !packageJson || (!packageJson.scripts && !hasMainFile);

        return {
          ...baseSettings,
          projectType: isGeneric ? 'generic' : 'javascript',
          minimal: true,
        };

      default:
        return baseSettings;
    }
  }

  async installClaudeHooks(targetDir) {
    return await this.hookGenerator.generate(targetDir);
  }

  async createFrameworkCommands(targetDir, config) {
    return await this.hookGenerator.createFrameworkCommands(targetDir, config);
  }

  determineMonorepoTool(hasTurbo, hasLerna, hasRush) {
    if (hasTurbo) {
      return 'turbo';
    }
    if (hasLerna) {
      return 'lerna';
    }
    if (hasRush) {
      return 'rush';
    }
    return 'npm';
  }

  getFrameworkScripts(projectType, config) {
    const baseScripts = {
      'claude:spawn': './scripts/python/spawn-agents.py',
      'claude:integrate': './scripts/python/integrate-parallel-work.py',
      'claude:monitor': './scripts/python/monitor-agents.py',
      'claude:validate': './scripts/python/validate-parallel-work.py',
      'claude:resolve': './scripts/python/resolve-conflicts.py',
    };

    const linearScripts = config.setupLinear
      ? {
          'claude:cache': './scripts/python/cache-linear-issue.py',
          'claude:decompose': './scripts/python/decompose-parallel.py',
        }
      : {};

    return { ...baseScripts, ...linearScripts };
  }

  getFrameworkCommands(projectType) {
    const commonCommands = {
      'component.md': `# Component Template

Create a new component with the following structure:

\`\`\`typescript
interface ComponentProps {
  // Define props here
}

export default function Component(props: ComponentProps) {
  return (
    <div>
      {/* Component implementation */}
    </div>
  );
}
\`\`\`
`,
      'test.md': `# Test Template

Create comprehensive tests:

\`\`\`typescript
describe('Component', () => {
  it('should render correctly', () => {
    // Test implementation
  });
});
\`\`\`
`,
    };

    switch (projectType) {
      case 'nextjs':
        return {
          ...commonCommands,
          'next-page-generator.md': `# Next.js Page Generator

\`\`\`typescript
export default function Page() {
  return (
    <div>
      <h1>Page Title</h1>
    </div>
  );
}
\`\`\`
`,
          'next-api-route.md': `# API Route Template

\`\`\`typescript
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  return NextResponse.json({ message: 'Hello World' });
}
\`\`\`
`,
        };

      case 'react':
        return {
          ...commonCommands,
          'react-component.md': `# React Component Template

\`\`\`typescript
interface ComponentProps {
  // Define props here
}

export default function Component(props: ComponentProps) {
  return (
    <div>
      {/* Component implementation */}
    </div>
  );
}
\`\`\`
`,
          'react-hook.md': `# React Hook Template

\`\`\`typescript
import { useState, useEffect } from 'react';

export function useCustomHook() {
  const [state, setState] = useState();
  
  useEffect(() => {
    // Effect logic
  }, []);
  
  return { state, setState };
}
\`\`\`
`,
        };

      case 'nodejs':
        return {
          ...commonCommands,
          'api-endpoint.md': `# Express Endpoint Template

\`\`\`typescript
import { Request, Response } from 'express';

export async function handler(req: Request, res: Response) {
  try {
    // Implementation
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}
\`\`\`
`,
          'middleware.md': `# Middleware Template

\`\`\`typescript
export function middleware(req: Request, res: Response, next: Function) {
  // Middleware logic
  next();
}
\`\`\`
`,
        };

      case 'python':
        return {
          'endpoint.md': `# Flask Endpoint Template

\`\`\`python
from flask import jsonify, request

@app.route('/api/endpoint', methods=['GET', 'POST'])
def endpoint():
    if request.method == 'GET':
        return jsonify({'message': 'Hello World'})
    
    data = request.get_json()
    return jsonify({'received': data})
\`\`\`
`,
          'model.md': `# Model Template

\`\`\`python
from dataclasses import dataclass
from typing import Optional

@dataclass
class Model:
    id: Optional[int] = None
    name: str = ""
\`\`\`
`,
        };

      default:
        return commonCommands;
    }
  }

  async setupEnvironmentVariables(targetDir, config) {
    return await this.templateGenerator.setupEnvironmentVariables(targetDir, config);
  }

  async setupGitHooks(targetDir) {
    console.log(chalk.gray('  • Setting up Git hooks...'));

    const gitHooksDir = path.join(targetDir, '.git', 'hooks');

    // Check if this is a Git repository
    if (!(await fs.pathExists(gitHooksDir))) {
      console.log(chalk.yellow('    Warning: Not a Git repository, skipping Git hooks setup'));
      return;
    }

    // Create pre-commit hook
    const preCommitPath = path.join(gitHooksDir, 'pre-commit');
    const preCommitContent = `#!/bin/bash

# Parallel Claude Development Workflow Pre-commit Hook

echo "Running pre-commit validation..."

# Check if this is an agent worktree
if [ -f "workspaces/*/validation_checklist.txt" ]; then
    echo "Agent worktree detected, running validation..."
    
    # Check if validation checklist is complete
    if grep -q "\\[ \\]" workspaces/*/validation_checklist.txt; then
        echo "❌ Validation checklist incomplete. Please complete all items before committing."
        exit 1
    fi
    
    echo "✅ Validation checklist complete"
fi

# Run any existing pre-commit hooks
if [ -f ".git/hooks/pre-commit.backup" ]; then
    .git/hooks/pre-commit.backup
fi

echo "Pre-commit validation passed"
`;

    // Backup existing pre-commit hook if it exists
    if (await fs.pathExists(preCommitPath)) {
      await fs.move(preCommitPath, `${preCommitPath}.backup`);
    }

    await fs.writeFile(preCommitPath, preCommitContent);
    await fs.chmod(preCommitPath, '755');
    console.log(chalk.gray(`    Created: pre-commit hook`));
  }

  async createExampleFiles(targetDir, config) {
    return await this.templateGenerator.createExampleFiles(targetDir, config);
  }

  async finalValidation(targetDir, _config) {
    console.log(chalk.gray('  • Running final validation...'));

    const requiredFiles = [
      'scripts/python/README.md',
      'scripts/python/cache-linear-issue.py',
      'scripts/python/decompose-parallel.py',
      'scripts/python/spawn-agents.py',
      'scripts/python/integrate-parallel-work.py',
      'scripts/python/monitor-agents.py',
      'scripts/python/validate-parallel-work.py',
      'scripts/python/resolve-conflicts.py',
      '.env.example',
    ];

    const missingFiles = [];

    for (const file of requiredFiles) {
      const filePath = path.join(targetDir, file);
      if (!(await fs.pathExists(filePath))) {
        missingFiles.push(file);
      }
    }

    if (missingFiles.length > 0) {
      console.log(chalk.yellow('    Warning: Some files were not created:'));
      missingFiles.forEach((file) => {
        console.log(chalk.yellow(`      - ${file}`));
      });
    } else {
      console.log(chalk.gray('    All required files created successfully'));
    }

    // Validate script permissions
    const scriptPath = path.join(targetDir, 'scripts', 'python', 'cache-linear-issue.py');
    if (await fs.pathExists(scriptPath)) {
      const stats = await fs.stat(scriptPath);
      if (stats.mode & 0o111) {
        console.log(chalk.gray('    Script permissions configured correctly'));
      } else {
        console.log(chalk.yellow('    Warning: Scripts may not be executable'));
      }
    }
  }

  async removeWorkflowDirectories(targetDir) {
    console.log(chalk.gray('  • Removing workflow directories...'));

    const directoriesToRemove = [
      'scripts/python',
      'shared/deployment-plans',
      'shared/coordination',
      'shared/reports',
      '.linear-cache',
    ];

    for (const dir of directoriesToRemove) {
      const fullPath = path.join(targetDir, dir);
      if (await fs.pathExists(fullPath)) {
        await fs.remove(fullPath);
        console.log(chalk.gray(`    Removed: ${dir}`));
      }
    }
  }

  async cleanupWorktrees(targetDir) {
    console.log(chalk.gray('  • Cleaning up worktrees...'));

    try {
      const { stdout } = await execAsync('git worktree list --porcelain', { cwd: targetDir });
      const worktrees = stdout.split('\n\n').filter(Boolean);

      for (const worktree of worktrees) {
        const lines = worktree.split('\n');
        const worktreePath = lines[0].replace('worktree ', '');
        const branchLine = lines.find((line) => line.startsWith('branch '));

        if (branchLine) {
          const branch = branchLine.replace('branch refs/heads/', '');

          // Remove worktree if it looks like an agent worktree
          if (branch.includes('agent')) {
            await execAsync(`git worktree remove ${worktreePath}`, { cwd: targetDir });
            console.log(chalk.gray(`    Removed worktree: ${worktreePath}`));
          }
        }
      }
    } catch (error) {
      console.log(chalk.yellow(`    Warning: Could not clean up worktrees: ${error.message}`));
    }
  }

  async removeConfigurationFiles(targetDir) {
    console.log(chalk.gray('  • Removing configuration files...'));

    const filesToRemove = ['.env.example'];

    for (const file of filesToRemove) {
      const fullPath = path.join(targetDir, file);
      if (await fs.pathExists(fullPath)) {
        await fs.remove(fullPath);
        console.log(chalk.gray(`    Removed: ${file}`));
      }
    }
  }
}

module.exports = { InstallSteps };
