# Constants Directory

This directory contains all hardcoded constants extracted from the CDEV codebase to follow the DRY (Don't Repeat Yourself) principle.

## Structure

- **index.js** - Main export file that re-exports all constants from sub-modules
- **directories.js** - All directory names and paths used in the project
- **frameworks.js** - Framework detection patterns and indicators
- **defaults.js** - Default values, configurations, and miscellaneous constants

## Usage

Import constants in your modules:

```javascript
// Import all constants
const constants = require('./constants');

// Import specific constant groups
const { directories, frameworks, defaults } = require('./constants');

// Import specific constants
const { CLAUDE_DIR, PROJECT_TYPES, DEFAULT_CONFIG } = require('./constants');
```

## Directory Constants (directories.js)

- Claude-specific directories (`.claude`, hooks, commands, etc.)
- Workflow directories
- Script directories
- Shared directories
- Cache directories
- Installation directory arrays

## Framework Constants (frameworks.js)

- Framework indicators (Next.js, React, Vue, Angular, etc.)
- Project type enums
- Monorepo tool indicators
- Package manager configurations
- Build tool indicators
- Test framework indicators
- TypeScript/ESLint/Prettier indicators

## Default Constants (defaults.js)

- Package and CLI constants
- Default environment values
- Default installation options
- File permissions
- Script patterns
- Expected file lists
- Template file paths
- Hook and command files
- Git configurations
- Environment variable names
- Dependencies
- Error and success messages
- Validation thresholds
- File names and extensions
- Ignored paths
- Claude permissions
- Example file configurations
- Worktree constants
- External URLs

## Guidelines

1. **Naming Convention**: Use UPPER_SNAKE_CASE for all constants
2. **Organization**: Group related constants together
3. **Documentation**: Add clear comments explaining each constant's purpose
4. **No Magic Values**: Replace all hardcoded strings and numbers with constants
5. **Exports**: Export all constants for easy importing
