/**
 * Directory structure constants for CDEV installation
 * Defines standard paths and directory names used throughout the system
 */

const path = require('path');

/**
 * Base directory names and paths
 */
const DIRECTORIES = {
  // Core directories
  WORKFLOWS: 'workflows',
  PARALLEL_CLAUDE: 'paralell-development-claude',

  // Script directories
  SCRIPTS: 'scripts',
  PYTHON_SCRIPTS: 'scripts/python',

  // Configuration directories
  CONFIGS: 'configs',
  HOOKS: 'hooks',

  // Working directories
  SHARED: 'shared',
  DEPLOYMENT_PLANS: 'shared/deployment-plans',
  LINEAR_CACHE: '.linear-cache',

  // Agent directories
  AGENTS: 'agents',
  WORKTREES: 'worktrees',

  // Template directories
  TEMPLATES: 'templates',
  TEMPLATE_CONFIGS: 'templates/configs',
  TEMPLATE_SCRIPTS: 'templates/scripts',
  TEMPLATE_HOOKS: 'templates/hooks',
  TEMPLATE_COMMANDS: 'templates/commands',
};

/**
 * Standard file names
 */
const FILES = {
  // Configuration files
  CONFIG: 'cdev.config.js',
  PACKAGE_JSON: 'package.json',
  ENV: '.env',

  // Cache files
  LINEAR_CACHE_PATTERN: '*.json',

  // Plan files
  DEPLOYMENT_PLAN_PATTERN: '*-deployment-plan.json',

  // Script files
  SPAWN_AGENTS: 'spawn-agents.py',
  MONITOR_AGENTS: 'monitor-agents.py',
  DECOMPOSE_PARALLEL: 'decompose-parallel.py',
  VALIDATE_WORK: 'validate-parallel-work.py',
  INTEGRATE_WORK: 'integrate-parallel-work.py',
};

/**
 * Path building utilities
 */
const PATHS = {
  /**
   * Get the full workflow path
   * @param {string} targetDir - Base target directory
   * @returns {string} Full workflow path
   */
  getWorkflowPath: (targetDir) =>
    path.join(targetDir, DIRECTORIES.WORKFLOWS, DIRECTORIES.PARALLEL_CLAUDE),

  /**
   * Get the scripts directory path
   * @param {string} targetDir - Base target directory
   * @returns {string} Scripts directory path
   */
  getScriptsPath: (targetDir) =>
    path.join(targetDir, DIRECTORIES.WORKFLOWS, DIRECTORIES.PARALLEL_CLAUDE, DIRECTORIES.SCRIPTS),

  /**
   * Get the Python scripts directory path
   * @param {string} targetDir - Base target directory
   * @returns {string} Python scripts directory path
   */
  getPythonScriptsPath: (targetDir) =>
    path.join(
      targetDir,
      DIRECTORIES.WORKFLOWS,
      DIRECTORIES.PARALLEL_CLAUDE,
      DIRECTORIES.PYTHON_SCRIPTS,
    ),

  /**
   * Get the shared directory path
   * @param {string} targetDir - Base target directory
   * @returns {string} Shared directory path
   */
  getSharedPath: (targetDir) => path.join(targetDir, DIRECTORIES.SHARED),

  /**
   * Get the Linear cache directory path
   * @param {string} targetDir - Base target directory
   * @returns {string} Linear cache directory path
   */
  getLinearCachePath: (targetDir) => path.join(targetDir, DIRECTORIES.LINEAR_CACHE),
};

module.exports = {
  DIRECTORIES,
  FILES,
  PATHS,
};
