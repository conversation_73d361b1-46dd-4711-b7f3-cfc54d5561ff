/**
 * Directory structure constants for the CDEV project
 * All directory names and paths used throughout the installation and operation
 */

// Claude-specific directories
const CLAUDE_DIR = '.claude';
const CLAUDE_HOOKS_DIR = '.claude/hooks';
const CLAUDE_COMMANDS_DIR = '.claude/commands';
const CLAUDE_LOGS_DIR = '.claude/logs';
const CLAUDE_TEMPLATES_DIR = '.claude/templates';
const CLAUDE_AGENTS_DIR = '.claude/agents';

// Hook tier directories
const HOOKS_TIER1_DIR = '.claude/hooks/tier1';
const HOOKS_TIER2_DIR = '.claude/hooks/tier2';
const HOOKS_TIER3_DIR = '.claude/hooks/tier3';

// Workflow directories
const WORKFLOWS_DIR = 'workflows';
const PARALLEL_WORKFLOW_DIR = 'workflows/paralell-development-claude';
const WORKFLOW_SCRIPTS_DIR = 'workflows/paralell-development-claude/scripts';
const WORKFLOW_AI_DOCS_DIR = 'workflows/paralell-development-claude/ai_docs';

// Script directories
const SCRIPTS_DIR = 'scripts';
const PYTHON_SCRIPTS_DIR = 'scripts/python';
const CHANGELOG_SCRIPTS_DIR = 'scripts/python/changelog';
const WRAPPER_SCRIPTS_DIR = 'scripts/wrappers';

// Shared directories
const SHARED_DIR = 'shared';
const DEPLOYMENT_PLANS_DIR = 'shared/deployment-plans';
const COORDINATION_DIR = 'shared/coordination';
const REPORTS_DIR = 'shared/reports';

// Cache directories
const LINEAR_CACHE_DIR = '.linear-cache';

// Workspace directories
const WORKSPACES_DIR = 'workspaces';

// Installation directory structure arrays
const INSTALLATION_DIRECTORIES = [
  CLAUDE_DIR,
  CLAUDE_HOOKS_DIR,
  CLAUDE_COMMANDS_DIR,
  CLAUDE_LOGS_DIR,
  CLAUDE_TEMPLATES_DIR,
  SCRIPTS_DIR,
  WORKFLOWS_DIR,
  PARALLEL_WORKFLOW_DIR,
  WORKFLOW_SCRIPTS_DIR,
  WORKFLOW_AI_DOCS_DIR,
  SHARED_DIR,
  DEPLOYMENT_PLANS_DIR,
  COORDINATION_DIR,
  REPORTS_DIR,
  LINEAR_CACHE_DIR,
];

const REQUIRED_DIRECTORIES = [
  CLAUDE_DIR,
  CLAUDE_HOOKS_DIR,
  CLAUDE_COMMANDS_DIR,
  SCRIPTS_DIR,
  WORKSPACES_DIR,
];

const WRITABLE_DIRECTORIES = [CLAUDE_DIR, WORKSPACES_DIR, SHARED_DIR];

module.exports = {
  // Individual directories
  CLAUDE_DIR,
  CLAUDE_HOOKS_DIR,
  CLAUDE_COMMANDS_DIR,
  CLAUDE_LOGS_DIR,
  CLAUDE_TEMPLATES_DIR,
  CLAUDE_AGENTS_DIR,
  HOOKS_TIER1_DIR,
  HOOKS_TIER2_DIR,
  HOOKS_TIER3_DIR,
  WORKFLOWS_DIR,
  PARALLEL_WORKFLOW_DIR,
  WORKFLOW_SCRIPTS_DIR,
  WORKFLOW_AI_DOCS_DIR,
  SCRIPTS_DIR,
  PYTHON_SCRIPTS_DIR,
  CHANGELOG_SCRIPTS_DIR,
  WRAPPER_SCRIPTS_DIR,
  SHARED_DIR,
  DEPLOYMENT_PLANS_DIR,
  COORDINATION_DIR,
  REPORTS_DIR,
  LINEAR_CACHE_DIR,
  WORKSPACES_DIR,

  // Directory arrays
  INSTALLATION_DIRECTORIES,
  REQUIRED_DIRECTORIES,
  WRITABLE_DIRECTORIES,
};
