/**
 * Main exports for all constants used in the CDEV project
 * Central location for importing all constants
 */

// Import all constants from sub-modules
const directories = require('./directories');
const frameworks = require('./frameworks');
const defaults = require('./defaults');

// Re-export all constants
module.exports = {
  // Export all directory constants
  ...directories,

  // Export all framework constants
  ...frameworks,

  // Export all default constants
  ...defaults,

  // Also export as named groups for cleaner imports
  directories,
  frameworks,
  defaults,
};
