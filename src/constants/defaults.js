/**
 * Default values and configurations for the CDEV project
 * Contains all default settings, configurations, and values
 */

// Package and CLI constants
const PACKAGE_NAME = '@aojdevstudio/cdev';
const CLI_COMMANDS = {
  NPX: 'npx cdev',
  NPX_FULL: 'npx @aojdevstudio/cdev',
  BINARY: 'cdev',
};
const LEGACY_CLI_NAME = 'claude-code-hooks';

// Default environment values
const DEFAULT_ENV_VALUES = {
  PROJECT_NAME: 'my-project',
  WORKTREE_PATH: '../worktrees',
  CLAUDE_MODEL: 'claude-3-5-sonnet-20241022',
  EDITOR: 'cursor',
};

// Default installation options
const DEFAULT_INSTALL_OPTIONS = {
  force: false,
  update: false,
  skipDependencies: false,
  verbose: false,
  setupLinear: false,
  linearApiKey: null,
  projectPath: process.cwd(),
};

// Default configuration values
const DEFAULT_CONFIG = {
  projectName: 'my-project',
  workTreePath: '../worktrees',
  projectType: 'minimal',
  packageManager: 'npm',
  typescript: false,
  setupLinear: false,
  linearApiKey: null,
};

// File permissions (octal)
const FILE_PERMISSIONS = {
  EXECUTABLE: 0o755, // rwxr-xr-x
  READABLE: 0o644, // rw-r--r--
  WRITABLE: 0o666, // rw-rw-rw-
};

// Script file patterns
const SCRIPT_PATTERNS = {
  SHELL_SCRIPTS: '*.sh',
  PYTHON_SCRIPTS: '*.py',
  NODE_SCRIPTS: '*.js',
  EXECUTABLE_EXTENSIONS: ['.sh', '.py', '.js', '.cjs'],
};

// Expected file lists for validation
const EXPECTED_FILES = {
  cli: ['claude-code-hooks'],
  config: ['.claude/hooks', '.claude/commands', '.claude/settings.json', '.claude/agents'],
  scripts: [
    'scripts/python/cache-linear-issue.py',
    'scripts/python/decompose-parallel.py',
    'scripts/python/spawn-agents.py',
  ],
  hooks: [
    '.claude/hooks/tier1/api-standards-checker.py',
    '.claude/hooks/tier1/code-quality-reporter.py',
    '.claude/hooks/tier2/typescript-validator.py',
  ],
};

// Python script templates
const PYTHON_SCRIPT_TEMPLATES = {
  BASE_SCRIPTS: [
    'scripts/python/spawn-agents.py',
    'scripts/python/monitor-agents.py',
    'scripts/python/agent-commit.py',
    'scripts/python/validate-parallel-work.py',
    'scripts/python/integrate-parallel-work.py',
    'scripts/python/resolve-conflicts.py',
    'scripts/python/intelligent-agent-generator.py',
    'scripts/python/security-check.py',
    'scripts/python/changelog/update-changelog.py',
    'scripts/python/changelog/utils.py',
    'scripts/python/changelog/README.md',
    'scripts/python/test-locally.py',
  ],
  LINEAR_SCRIPTS: ['scripts/python/cache-linear-issue.py', 'scripts/python/decompose-parallel.py'],
};

// Wrapper script templates
const WRAPPER_SCRIPT_TEMPLATES = [
  'scripts/wrappers/deploy.sh',
  'scripts/wrappers/spawn-agents.sh',
  'scripts/wrappers/monitor-agents.sh',
];

// Hook file names
const HOOK_FILES = [
  'pre_tool_use.py',
  'post_tool_use.py',
  'stop.py',
  'subagent_stop.py',
  'notification.py',
  'typescript-validator.py',
  'code-quality-reporter.py',
  'api-standards-checker.py',
  'import-organizer.py',
  'auto_commit_on_changes.py',
  'universal-linter.py',
  'pnpm-enforcer.py',
];

// Command file names
const COMMAND_FILES = {
  'component.md': 'Component creation template',
  'test.md': 'Test creation template',
  'update-changelog.md': 'Changelog update command',
};

// Git hook configurations
const GIT_HOOKS = {
  PRE_COMMIT: 'pre-commit',
  PRE_COMMIT_BACKUP: 'pre-commit.backup',
  POST_COMMIT: 'post-commit',
  PRE_PUSH: 'pre-push',
};

// Environment variable names
const ENV_VARS = {
  LINEAR_API_KEY: 'LINEAR_API_KEY',
  PROJECT_NAME: 'PROJECT_NAME',
  WORKTREE_PATH: 'WORKTREE_PATH',
  CLAUDE_MODEL: 'CLAUDE_MODEL',
  EDITOR: 'EDITOR',
  CLAUDE_PROJECT_DIR: 'CLAUDE_PROJECT_DIR',
};

// Package dependencies
const DEPENDENCIES = {
  REQUIRED: {
    dotenv: '^16.6.1',
  },
  DEV: {},
};

// System dependencies
const SYSTEM_DEPENDENCIES = [
  { name: 'Node.js', command: 'node --version', required: true },
  { name: 'Git', command: 'git --version', required: true },
  { name: 'Claude Code', command: 'claude --version', required: false },
  { name: 'npm', command: 'npm --version', required: true },
];

// Hook matchers for Claude settings
const HOOK_MATCHERS = {
  ALL: '',
  WRITE_EDIT: 'Write|Edit',
};

// Framework-specific script names
const FRAMEWORK_SCRIPTS = {
  BASE: {
    'claude:spawn': './scripts/python/spawn-agents.py',
    'claude:integrate': './scripts/python/integrate-parallel-work.py',
    'claude:monitor': './scripts/python/monitor-agents.py',
    'claude:validate': './scripts/python/validate-parallel-work.py',
    'claude:resolve': './scripts/python/resolve-conflicts.py',
  },
  LINEAR: {
    'claude:cache': './scripts/python/cache-linear-issue.py',
    'claude:decompose': './scripts/python/decompose-parallel.py',
  },
  PARALLEL: {
    decompose: 'node workflows/paralell-development-claude/scripts/decompose-parallel.cjs',
    'spawn-agents': 'workflows/paralell-development-claude/scripts/spawn-agents.sh',
    'cache-issue': 'workflows/paralell-development-claude/scripts/cache-linear-issue.sh',
  },
  CHANGELOG: {
    'changelog:update':
      'node workflows/paralell-development-claude/scripts/changelog/update-changelog.js',
    'changelog:auto':
      'node workflows/paralell-development-claude/scripts/changelog/update-changelog.js --auto',
    'changelog:manual':
      'node workflows/paralell-development-claude/scripts/changelog/update-changelog.js --manual',
    'changelog:preview':
      'node workflows/paralell-development-claude/scripts/changelog/update-changelog.js --auto --dry-run',
  },
};

// Error messages
const ERROR_MESSAGES = {
  MISSING_DEPENDENCIES: 'Missing required dependencies',
  NO_WRITE_PERMISSION: 'No write permission to directory',
  WORKFLOW_EXISTS: 'Directory already contains a workflow. Use --force to overwrite.',
  PYTHON_NOT_AVAILABLE: 'Python not available for hooks',
  TEMPLATE_NOT_FOUND: 'Template not found',
};

// Success messages
const SUCCESS_MESSAGES = {
  ALL_HOOKS_INSTALLED: 'All hooks are properly installed',
  ALL_FILES_CREATED: 'All required files created successfully',
  PERMISSIONS_CORRECT: 'All file permissions are correct',
  CONFIGURATION_VALID: 'All configuration files are valid',
  PYTHON_HOOKS_FUNCTIONAL: 'Python hooks functional with Python',
};

// Validation thresholds
const VALIDATION_THRESHOLDS = {
  MIN_PYTHON_VERSION: '3.6',
  MAX_PATH_LENGTH: 260, // Windows path limit
  MIN_NODE_VERSION: '14.0.0',
};

// File names and patterns
const FILE_NAMES = {
  PACKAGE_JSON: 'package.json',
  TSCONFIG_JSON: 'tsconfig.json',
  ENV: '.env',
  ENV_EXAMPLE: '.env.example',
  ENV_LOCAL: '.env.local',
  ENV_DEVELOPMENT: '.env.development.local',
  ENV_TEST: '.env.test.local',
  ENV_PRODUCTION: '.env.production.local',
  GITIGNORE: '.gitignore',
  CHANGELOG: 'CHANGELOG.md',
  README: 'README.md',
  CLAUDE_SETTINGS: '.claude/settings.json',
};

// Git-related constants
const GIT_CONSTANTS = {
  GIT_DIR: '.git',
  GIT_HOOKS_DIR: '.git/hooks',
  WORKTREE_COMMAND: 'git worktree',
};

// Ignored directories and patterns
const IGNORED_PATHS = [
  'node_modules',
  '.git',
  'dist',
  'build',
  '__pycache__',
  '*.pyc',
  '*.pyo',
  '*.pyd',
  '.DS_Store',
  'Thumbs.db',
  '.vscode',
  '.idea',
  '.next',
  '*.log',
];

// File extensions
const FILE_EXTENSIONS = {
  JAVASCRIPT: ['.js', '.jsx', '.mjs', '.cjs'],
  TYPESCRIPT: ['.ts', '.tsx'],
  PYTHON: ['.py'],
  SHELL: ['.sh'],
  MARKDOWN: ['.md'],
  JSON: ['.json'],
  YAML: ['.yaml', '.yml'],
};

// Claude hook command patterns
const CLAUDE_HOOK_COMMANDS = {
  PRE_TOOL_USE: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/pre_tool_use.py',
  POST_TOOL_USE: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/post_tool_use.py',
  IMPORT_ORGANIZER: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/import-organizer.py',
  AUTO_COMMIT: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/auto_commit_on_changes.py',
  UNIVERSAL_LINTER: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/universal-linter.py',
  NOTIFICATION: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/notification.py --notify',
  STOP: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/stop.py --chat',
  SUBAGENT_STOP: 'cd "$CLAUDE_PROJECT_DIR" && uv run .claude/hooks/subagent_stop.py',
};

// Python shebang patterns
const PYTHON_SHEBANGS = [
  '#!/usr/bin/env python',
  '#!/usr/bin/env python3',
  '#!/usr/bin/python',
  '#!/usr/bin/python3',
];

// Claude permissions for settings.json
const CLAUDE_PERMISSIONS = {
  allow: [
    'Bash(mkdir:*)',
    'Bash(uv:*)',
    'Bash(find:*)',
    'Bash(mv:*)',
    'Bash(grep:*)',
    'Bash(npm:*)',
    'Bash(ls:*)',
    'Bash(cp:*)',
    'Write',
    'Edit',
    'Bash(chmod:*)',
    'Bash(touch:*)',
  ],
  deny: [],
};

// Example file constants
const EXAMPLE_FILES = {
  LINEAR_ISSUE: {
    ID: 'EXAMPLE-123',
    IDENTIFIER: 'EXAMPLE-123',
    TITLE: 'Example: Implement user authentication system',
    DESCRIPTION:
      'This is an example Linear issue showing how to structure requirements for parallel development.\n\n1. Create login/signup forms\n2. Implement JWT authentication\n3. Add password reset functionality\n4. Create user profile management\n5. Add role-based access control',
    PRIORITY: 1,
    PRIORITY_LABEL: 'High',
    STATE: 'Todo',
    ASSIGNEE_NAME: 'Developer',
    ASSIGNEE_EMAIL: '<EMAIL>',
    TEAM: 'Engineering',
    FILE_NAME: 'EXAMPLE-123.json',
  },
  DEPLOYMENT_PLAN: {
    FILE_NAME: 'example-123-deployment-plan.json',
    AGENT_IDS: {
      FRONTEND: 'frontend_forms_agent',
      BACKEND: 'backend_auth_agent',
    },
    ESTIMATED_TIMES: {
      FRONTEND: 30,
      BACKEND: 45,
      TOTAL: '45 minutes',
      INTEGRATION: '15 minutes',
    },
    PARALLELISM_FACTOR: '1.5x faster than sequential',
  },
};

// Worktree constants
const WORKTREE_CONSTANTS = {
  AGENT_BRANCH_PATTERN: 'agent',
  VALIDATION_CHECKLIST_FILE: 'validation_checklist.txt',
  CHECKLIST_INCOMPLETE_PATTERN: '\\[ \\]',
};

// External URLs
const EXTERNAL_URLS = {
  NODEJS: 'https://nodejs.org/',
  GIT: 'https://git-scm.com/',
  CLAUDE_CODE: 'https://claude.ai/code',
};

module.exports = {
  // Package and CLI constants
  PACKAGE_NAME,
  CLI_COMMANDS,
  LEGACY_CLI_NAME,

  // Default values
  DEFAULT_ENV_VALUES,
  DEFAULT_INSTALL_OPTIONS,
  DEFAULT_CONFIG,

  // File permissions
  FILE_PERMISSIONS,

  // Patterns
  SCRIPT_PATTERNS,

  // Expected files
  EXPECTED_FILES,

  // Templates
  PYTHON_SCRIPT_TEMPLATES,
  WRAPPER_SCRIPT_TEMPLATES,

  // File lists
  HOOK_FILES,
  COMMAND_FILES,

  // Git configurations
  GIT_HOOKS,

  // Environment variables
  ENV_VARS,

  // Dependencies
  DEPENDENCIES,
  SYSTEM_DEPENDENCIES,

  // Hook configurations
  HOOK_MATCHERS,

  // Scripts
  FRAMEWORK_SCRIPTS,

  // Messages
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,

  // Validation
  VALIDATION_THRESHOLDS,

  // File names and patterns
  FILE_NAMES,

  // Git constants
  GIT_CONSTANTS,

  // Ignored paths
  IGNORED_PATHS,

  // File extensions
  FILE_EXTENSIONS,

  // Claude hook commands
  CLAUDE_HOOK_COMMANDS,

  // Python shebangs
  PYTHON_SHEBANGS,

  // Claude permissions
  CLAUDE_PERMISSIONS,

  // Example files
  EXAMPLE_FILES,

  // Worktree constants
  WORKTREE_CONSTANTS,

  // External URLs
  EXTERNAL_URLS,
};
