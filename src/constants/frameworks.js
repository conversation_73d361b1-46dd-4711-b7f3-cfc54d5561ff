/**
 * Framework detection constants and indicators
 * Used to identify project types and configure appropriate workflows
 */

/**
 * Framework detection patterns
 * Maps framework identifiers to their detection patterns
 */
const FRAMEWORK_INDICATORS = {
  // Frontend frameworks
  REACT: {
    files: ['package.json'],
    patterns: ['"react":', 'create-react-app', '@types/react'],
    directories: ['src/components', 'public'],
    extensions: ['.jsx', '.tsx'],
  },

  NEXT: {
    files: ['next.config.js', 'next.config.ts', 'package.json'],
    patterns: ['"next":', 'next/router', 'next/head'],
    directories: ['pages', 'app'],
    extensions: ['.jsx', '.tsx'],
  },

  VUE: {
    files: ['vue.config.js', 'package.json'],
    patterns: ['"vue":', '@vue/cli', 'vue-router'],
    directories: ['src/components', 'src/views'],
    extensions: ['.vue'],
  },

  ANGULAR: {
    files: ['angular.json', 'package.json'],
    patterns: ['"@angular/', 'ng serve', 'angular-cli'],
    directories: ['src/app'],
    extensions: ['.component.ts', '.module.ts'],
  },

  // Backend frameworks
  EXPRESS: {
    files: ['package.json'],
    patterns: ['"express":', 'app.listen', 'express()'],
    directories: ['routes', 'middleware'],
    extensions: ['.js', '.ts'],
  },

  FASTIFY: {
    files: ['package.json'],
    patterns: ['"fastify":', 'fastify.register', 'fastify()'],
    directories: ['routes', 'plugins'],
    extensions: ['.js', '.ts'],
  },

  DJANGO: {
    files: ['manage.py', 'requirements.txt', 'Pipfile'],
    patterns: ['django', 'Django', 'DJANGO_SETTINGS_MODULE'],
    directories: ['templates', 'static'],
    extensions: ['.py'],
  },

  FLASK: {
    files: ['app.py', 'requirements.txt', 'Pipfile'],
    patterns: ['flask', 'Flask', 'from flask import'],
    directories: ['templates', 'static'],
    extensions: ['.py'],
  },

  // Full-stack frameworks
  NUXT: {
    files: ['nuxt.config.js', 'nuxt.config.ts', 'package.json'],
    patterns: ['"nuxt":', '@nuxt/', 'nuxt generate'],
    directories: ['pages', 'components', 'layouts'],
    extensions: ['.vue'],
  },

  GATSBY: {
    files: ['gatsby-config.js', 'gatsby-node.js', 'package.json'],
    patterns: ['"gatsby":', 'gatsby-', 'StaticQuery'],
    directories: ['src/pages', 'src/components'],
    extensions: ['.jsx', '.tsx'],
  },

  // Build tools and bundlers
  WEBPACK: {
    files: ['webpack.config.js', 'webpack.config.ts'],
    patterns: ['webpack', 'module.exports', 'entry:'],
    directories: ['dist', 'build'],
    extensions: ['.js', '.ts'],
  },

  VITE: {
    files: ['vite.config.js', 'vite.config.ts', 'package.json'],
    patterns: ['"vite":', 'import { defineConfig }', 'vite build'],
    directories: ['dist'],
    extensions: ['.js', '.ts'],
  },

  // Package managers
  NPM: {
    files: ['package.json', 'package-lock.json'],
    patterns: ['npm install', 'npm run', 'npm start'],
    directories: ['node_modules'],
    extensions: ['.js', '.ts'],
  },

  YARN: {
    files: ['yarn.lock', 'package.json'],
    patterns: ['yarn install', 'yarn run', 'yarn start'],
    directories: ['node_modules'],
    extensions: ['.js', '.ts'],
  },

  PNPM: {
    files: ['pnpm-lock.yaml', 'package.json'],
    patterns: ['pnpm install', 'pnpm run', 'pnpm start'],
    directories: ['node_modules'],
    extensions: ['.js', '.ts'],
  },
};

/**
 * Framework categories for grouping and workflows
 */
const FRAMEWORK_CATEGORIES = {
  FRONTEND: ['REACT', 'VUE', 'ANGULAR', 'NEXT', 'NUXT', 'GATSBY'],
  BACKEND: ['EXPRESS', 'FASTIFY', 'DJANGO', 'FLASK'],
  FULLSTACK: ['NEXT', 'NUXT', 'GATSBY'],
  BUILD_TOOLS: ['WEBPACK', 'VITE'],
  PACKAGE_MANAGERS: ['NPM', 'YARN', 'PNPM'],
};

/**
 * Priority order for framework detection
 * Higher priority frameworks are checked first
 */
const DETECTION_PRIORITY = [
  'NEXT', // Specific React framework
  'NUXT', // Specific Vue framework
  'GATSBY', // Specific React framework
  'REACT', // General React
  'VUE', // General Vue
  'ANGULAR', // Angular
  'DJANGO', // Python web framework
  'FLASK', // Python web framework
  'EXPRESS', // Node.js framework
  'FASTIFY', // Node.js framework
  'VITE', // Modern build tool
  'WEBPACK', // Traditional build tool
  'YARN', // Package manager
  'PNPM', // Package manager
  'NPM', // Default package manager
];

/**
 * Default configurations for detected frameworks
 */
const FRAMEWORK_DEFAULTS = {
  REACT: {
    testCommand: 'npm test',
    buildCommand: 'npm run build',
    devCommand: 'npm start',
    lintCommand: 'npm run lint',
  },

  NEXT: {
    testCommand: 'npm test',
    buildCommand: 'npm run build',
    devCommand: 'npm run dev',
    lintCommand: 'npm run lint',
  },

  VUE: {
    testCommand: 'npm run test:unit',
    buildCommand: 'npm run build',
    devCommand: 'npm run serve',
    lintCommand: 'npm run lint',
  },

  DJANGO: {
    testCommand: 'python manage.py test',
    buildCommand: 'python manage.py collectstatic',
    devCommand: 'python manage.py runserver',
    lintCommand: 'flake8',
  },

  EXPRESS: {
    testCommand: 'npm test',
    buildCommand: 'npm run build',
    devCommand: 'npm run dev',
    lintCommand: 'npm run lint',
  },
};

module.exports = {
  FRAMEWORK_INDICATORS,
  FRAMEWORK_CATEGORIES,
  DETECTION_PRIORITY,
  FRAMEWORK_DEFAULTS,
};
