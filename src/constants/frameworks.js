/**
 * Framework detection and configuration constants
 * Patterns and indicators for detecting various frameworks and project types
 */

// Framework indicators - files that indicate specific frameworks
const FRAMEWORK_INDICATORS = {
  nextjs: {
    files: ['next.config.js', 'next.config.mjs', 'next.config.ts'],
    packageDependencies: ['next'],
    description: 'Next.js React framework',
  },
  react: {
    files: ['src/App.js', 'src/App.jsx', 'src/App.tsx'],
    packageDependencies: ['react', 'react-dom'],
    description: 'React application',
  },
  vue: {
    files: ['vue.config.js', 'src/App.vue'],
    packageDependencies: ['vue'],
    description: 'Vue.js application',
  },
  angular: {
    files: ['angular.json', '.angular.json'],
    packageDependencies: ['@angular/core'],
    description: 'Angular application',
  },
  express: {
    files: ['app.js', 'server.js'],
    packageDependencies: ['express'],
    description: 'Express.js server',
  },
  nodejs: {
    files: ['index.js', 'main.js', 'server.js'],
    packageDependencies: [],
    description: 'Node.js application',
  },
  python: {
    files: ['app.py', 'main.py', 'manage.py', 'requirements.txt', 'setup.py', 'pyproject.toml'],
    packageDependencies: [],
    description: 'Python application',
  },
};

// Project type patterns
const PROJECT_TYPES = {
  NEXTJS: 'nextjs',
  REACT: 'react',
  VUE: 'vue',
  ANGULAR: 'angular',
  NODEJS: 'nodejs',
  PYTHON: 'python',
  MONOREPO: 'monorepo',
  MINIMAL: 'minimal',
  GENERIC: 'generic',
};

// Monorepo tool indicators
const MONOREPO_INDICATORS = {
  turbo: {
    file: 'turbo.json',
    command: 'turbo',
    description: 'Turborepo monorepo',
  },
  lerna: {
    file: 'lerna.json',
    command: 'lerna',
    description: 'Lerna monorepo',
  },
  rush: {
    file: 'rush.json',
    command: 'rush',
    description: 'Rush monorepo',
  },
  nx: {
    file: 'nx.json',
    command: 'nx',
    description: 'Nx monorepo',
  },
  pnpm: {
    file: 'pnpm-workspace.yaml',
    command: 'pnpm',
    description: 'pnpm workspace',
  },
  yarn: {
    file: 'yarn.lock',
    packageJson: { workspaces: true },
    command: 'yarn',
    description: 'Yarn workspaces',
  },
};

// Package manager indicators
const PACKAGE_MANAGERS = {
  npm: {
    lockFile: 'package-lock.json',
    command: 'npm',
    installCommand: 'npm install',
    runCommand: 'npm run',
  },
  yarn: {
    lockFile: 'yarn.lock',
    command: 'yarn',
    installCommand: 'yarn install',
    runCommand: 'yarn',
  },
  pnpm: {
    lockFile: 'pnpm-lock.yaml',
    command: 'pnpm',
    installCommand: 'pnpm install',
    runCommand: 'pnpm',
  },
  bun: {
    lockFile: 'bun.lockb',
    command: 'bun',
    installCommand: 'bun install',
    runCommand: 'bun run',
  },
};

// Build tool indicators
const BUILD_TOOLS = {
  webpack: {
    files: ['webpack.config.js', 'webpack.config.ts'],
    description: 'Webpack bundler',
  },
  vite: {
    files: ['vite.config.js', 'vite.config.ts'],
    description: 'Vite build tool',
  },
  rollup: {
    files: ['rollup.config.js', 'rollup.config.ts'],
    description: 'Rollup bundler',
  },
  esbuild: {
    files: ['esbuild.config.js', 'esbuild.config.ts'],
    description: 'ESBuild bundler',
  },
  parcel: {
    files: ['.parcelrc'],
    description: 'Parcel bundler',
  },
};

// Test framework indicators
const TEST_FRAMEWORKS = {
  jest: {
    files: ['jest.config.js', 'jest.config.ts'],
    packageDependencies: ['jest'],
    description: 'Jest testing framework',
  },
  vitest: {
    files: ['vitest.config.js', 'vitest.config.ts'],
    packageDependencies: ['vitest'],
    description: 'Vitest testing framework',
  },
  mocha: {
    files: ['.mocharc.js', '.mocharc.json'],
    packageDependencies: ['mocha'],
    description: 'Mocha testing framework',
  },
  cypress: {
    files: ['cypress.json', 'cypress.config.js'],
    packageDependencies: ['cypress'],
    description: 'Cypress E2E testing',
  },
  playwright: {
    files: ['playwright.config.js', 'playwright.config.ts'],
    packageDependencies: ['@playwright/test'],
    description: 'Playwright testing',
  },
};

// TypeScript indicators
const TYPESCRIPT_INDICATORS = {
  configFile: 'tsconfig.json',
  extensions: ['.ts', '.tsx'],
  packageDependencies: ['typescript'],
};

// ESLint indicators
const ESLINT_INDICATORS = {
  configFiles: ['.eslintrc', '.eslintrc.js', '.eslintrc.json', 'eslint.config.js'],
  packageDependencies: ['eslint'],
};

// Prettier indicators
const PRETTIER_INDICATORS = {
  configFiles: ['.prettierrc', '.prettierrc.js', '.prettierrc.json', 'prettier.config.js'],
  packageDependencies: ['prettier'],
};

module.exports = {
  FRAMEWORK_INDICATORS,
  PROJECT_TYPES,
  MONOREPO_INDICATORS,
  PACKAGE_MANAGERS,
  BUILD_TOOLS,
  TEST_FRAMEWORKS,
  TYPESCRIPT_INDICATORS,
  ESLINT_INDICATORS,
  PRETTIER_INDICATORS,
};
